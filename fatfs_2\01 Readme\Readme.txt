# 2025��CIMC�й�����������ս��-��ҵǶ��ʽϵͳ��������

# Program��CIMC_GD32_fatfs_driver

## ������
- �������ƣ�GD32F470 fatfs ʵ��
- ʵ��ƽ̨: CIMC IHD V0.4
- MDK�汾��5.25


## ������Դ

 - GD32F470VET6 MCU
 
 
## ���ܼ��

����ģ�壬��������������������


## ʵ�����

�������ʼ��tf����tf������ǰ��ʽ��Ϊfat32��
ϵͳ֧�������������ָ��

### ��������
ϵͳ֧�ֵ��ַ�������ָ��

1. **t ��� T** - ϵͳ�Լ�����
   - ���TF��״̬
   - ��ʾTF������Ϣ
   - ���ʽ��
     - TF��������ʱ��TF card ............ ok / TF card memory: XXxxx KB
     - TF�������ʱ��TF card............ error / can not find TF card

2. **w ��� W** - �ļ�д������
   - ��ʾ��Input data (press Enter to save):
   - ͨ �����ڷ������ݣ�ϵͳ�����ļ�ϵͳ�в���FATFS.TXT�ļ�
   - ���У���ֱ�Ӵ򿪣���û�У��򴴽����ļ�
   - �򿪺�����ļ�д�봮�ڷ��͵�����
   - д����ɺ󣬴򿪸��ļ������ж�ȡ��

3. **f ��� F** - FLASH����
   - ���SPI FLASH ID
   - ִ��FLASH��д����
   - ��ʾFLASH״̬

4. **?** - ���ٽ��Լ�����
   - ��t/T����ͬ������ִ��ϵͳ�Լ�

### ������ָ��
ϵͳ֧�������������ָ��

1. **test** - ϵͳ�Լ�����
   - ���FLASH״̬
   - ���TF��״̬
   - ��ʾTF������Ϣ

2. **conf** - �����ļ���ȡ
   - ��TF����ȡconfig.ini�����ļ�
   - ����Ratio������Limit������������
   - �����������ݱ���Flash�洢��
   - ���ʽ��
     - �ļ������ʱ��config.ini file not found.
     - ��ȡ�ɹ�ʱ��
       ```
       Ratio=XXXX
       Limit = XXXX
       config read success
       ```

3. **ratio** - ������������
   - ��ʾ��ǰ����ֵ
   - ��ʾ�����µ�����ֵ��Χ0~100
   - ��֤�����Ч��
   - ���ʽ��
     - �ɹ�����ʱ��
       ```
       Ratio=1.0
       Input value(0~100):
       10.5
       ratio modified success
       Ratio = 10.5
       ```
     - ��Ч����ʱ��
       ```
       Ratio = 1.0
       Input value(0~100):
       100.5
       ratio invalid
       Ratio = 1.0
       ```

4. **limit** - ��ֵ����
   - ��ʾ��ǰ��ֵ
   - ��ʾ�����µ���ֵ��Χ0~500
   - ��֤�����Ч��
   - ���ʽ��
     - �ɹ�����ʱ��
       ```
       limit=1.0
       Input value(0~500):
       50.12
       limit modified success
       limit = 50.12
       ```
     - ��Ч����ʱ��
       ```
       limit =1.0
       Input value(0~500):
       510.12
       limit invalid
       limit = 1.0
       ```

5. **config save** - ��������Flash
   - ��ʾ��ǰ��������ֵ
   - ������ݱ���Flash�洢��
   - ���ʽ��
     ```
     ratio: 20.5
     limit: 100.00
     save parameters to flash
     ```

6. **config read** - ��Flash��ȡ����
   - ��Flash�洢����ȡ����
   - ��ʾ��ȡ������ֵ
   - ���ʽ��
     ```
     read parameters from flash
     ratio: 20.5
     limit: 100.00
     ```

7. **start** - ��������
   - ��������ڲ���ģʽ
   - LED1��˸ָʾ��1s����
   - OLED��ʾʱ�����ѹֵ
   - ÿ5�����һ�β���
   - ���ʽ��
     ```
     Periodic Sampling
     sample cycle: 5s
     2025-01-01 00:30:05 ch0=10.50V
     2025-01-01 00:30:10 ch0=10.50V
     ```

8. **stop** - ֹͣ����
   - ֹͣ��������
   - LED1����
   - OLED��ʾ"system idle"
   - ���ʽ��
     ```
     Periodic Sampling STOP
     ```

9. **tragll** - �ļ�д������
   - �����д��FATFS.TXT�ļ�
   - ��֤�ļ���д����

### ��������
ϵͳ֧�ֲ����־û��洢����
- **�Զ�����**��ratio��limit���������޸ĺ��Զ�����Flash
- **�ֶ�����**��ʹ��config save�����ֶ�����
- **�Զ�����**��ϵͳ����ʱ�Զ���Flash�м��ز���
- **�ϵ籣��**��Flash�洢ȷ���ϵ�����������ʧ

### ��������
ϵͳ֧�ֶ�ʱ������ܺ�ʵʱ��ʾ����
- **LED1��˸**��������ʱ1s����˸ָʾ״̬
- **OLED��ʾ**��ʵʱ��ʾʱ�䣨hh:mm:ss����ѹ��xx.xxV��
- **ADC����**��PC0ͨ����ȡ��ѹֵ��0-3.3V��Χ
- **RTC��ʱ**��ʵʱʱ��ϵͳ��ʱ���ݲ���
- **������**��5s����ڲ�����ʱ���ѹ���

### config.ini�ļ���ʽ
�����ļ���Ӧ��TF����Ŀ¼�£��ļ����ݸ�ʽ��
```
Ratio=12.5
Limit=56.78
```
- Ratio: ����ֵ��������0~100��
- Limit: ��ֵ��������0~500��
- ÿ��һ�У�ʹ��=�ŷָ�������ֵ
- ֧�ָ�����

### ʹ�÷���
1. ϵͳ����ʱ��ʾ��System ready. Enter single character commands:
2. ����Ӧ�ĵ��ַ�ָ��
3. ϵͳ��ֱ��ִ�ж�Ӧ����
4. ÿ������ִ�к󣬻ᱻ�Զ�����

### ϵͳ�Լ�����
ͨ������"test"ָ��ִ��ϵͳ�Լ�������ʽ��

**�ɹ�����**:
```
====== system selftest ======
flash ............ ok
TF card ............ ok
flash ID: 0xCxxxxx
TF card memory: XXxxx KB
====== system selftest ======
```

**ʧ������**:
```
====== system selftest ======
flash ............ ok
TF card............ error
flash ID: 0xCxxxxx
can not find TF card
====== system selftest ======
```

- FLASH����: ���FLASH ID�Ƿ���ȷ
- TF������: ���TF��״̬������Ϣ

## ���ŷ���

PE2  --  SD_CD
PD2  --  SD_CMD
PC8  --  SD_DAT0
PC9  --  SD_DAT1
PC10 --  SD_DAT2
PC11 --  SD_DAT3
PC12 --  SD_CLK

## ����汾

- ����汾��V0.7
- �������ڣ�2025-06-16
- ������־��
  - V0.7: ���������ƿ���(start/stop)��LED��˸��OLED��ʾ��ADC����
  - V0.6: ���������洢����(config save/read)���ϵ籣������
  - V0.5: ������ֵ������(limit)������֤�����Ч��
  - V0.4: ���������������(ratio)������֤�����Ч��
  - V0.3: ���������ļ���ȡ����(conf)������Flash�洢����
  - V0.2: ���ϵͳ�Լ�����(test)��������ָ��(tragll)
  - V0.1: ��������FatFS����

## ��ϵ����

- Copyright   : CIMC�й�����������ս��
- Author      ��Lingyu Meng
- Website     ��www.siemenscup-cimc.org.cn
- Phone       ��15801122380

## ����

**�Ͻ���ҵ��;������ѧϰʹ�á� **


## Ŀ¼�ṹ

����01 Readme		������Ŀ˵��
����CMSIS			�ں������ļ���Cortex Microcontroller Software Interface Standard
����Function		�û�����
����HardWare		Ӳ������
����HeaderFiles	ͷ�ļ�����
����Library		���ļ�
��  ����GD32F4xx_standard_peripheral
��  ����GD32F4xx_usb_library
��  ����Third_Party
����project		�����ļ��������ɵ������ļ���
����Protocol		Э�����
����Startup		�����ļ�
����System		
����User
