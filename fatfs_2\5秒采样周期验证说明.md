# 5秒采样周期验证说明

## 采样周期实现原理

### 时序控制机制
系统采用基于计数器的精确时序控制：

```c
// 采样控制相关变量
uint32_t sample_cycle = 5;       // 采样周期（秒）
uint32_t sample_counter = 0;     // 采样计数器

// 在update_sampling()函数中
sample_counter++;
if(sample_counter >= (sample_cycle * 1000)) { // 5000次调用 = 5秒
    // 执行采样并输出数据
    printf("%s ch0=%.1fV\r\n", time_str, voltage);
    sample_counter = 0; // 重置计数器
}
```

### 调用频率分析
- **主循环调用频率：** 约1ms一次调用update_sampling()
- **采样周期计算：** 5秒 × 1000次/秒 = 5000次调用
- **实际周期精度：** ±1ms（取决于主循环执行时间）

## 预期输出格式

### 启动采样后的输出序列
```
输入: start

输出:
Periodic Sampling
sample cycle: 5s
[OLED] 12:00:05 | 10.50 V
2025-01-01 12:00:05 ch0=10.5V    ← 第1次采样（启动后5秒）
2025-01-01 12:00:10 ch0=10.6V    ← 第2次采样（启动后10秒）
2025-01-01 12:00:15 ch0=10.4V    ← 第3次采样（启动后15秒）
2025-01-01 12:00:20 ch0=10.5V    ← 第4次采样（启动后20秒）
...
```

### 时间戳精度验证
- **时间源：** 真实RTC硬件时钟
- **时间格式：** `2025-01-01 12:00:05`（年-月-日 时:分:秒）
- **递增精度：** 精确5秒间隔
- **同步机制：** 每次采样时实时读取RTC

## 验证测试步骤

### 测试1：基本5秒周期验证
```bash
# 1. 设置准确的RTC时间
RTC Config
2025-01-01 12:00:00

# 2. 启动采样
start

# 3. 观察输出时间戳
# 预期：12:00:05, 12:00:10, 12:00:15, 12:00:20...
```

**验证要点：**
- ✅ 第一次采样在启动后5秒输出
- ✅ 后续每次采样间隔精确5秒
- ✅ 时间戳与实际时间同步

### 测试2：长时间稳定性验证
```bash
# 启动采样并观察30分钟
start
# 观察360次采样输出（30分钟 ÷ 5秒 = 360次）
# 验证时间戳连续性和周期稳定性
```

**验证要点：**
- ✅ 长时间运行无时间漂移
- ✅ 采样间隔始终保持5秒
- ✅ 无丢失或重复采样

### 测试3：启停重复验证
```bash
# 多次启停测试
start
# 等待几个采样周期
stop
start
# 验证重新启动后周期重置
```

**验证要点：**
- ✅ 停止后重新启动，周期重新开始计时
- ✅ 每次启动后第一次采样都在5秒后
- ✅ 启停操作不影响时间精度

## 技术实现细节

### 计数器重置机制
```c
void start_sampling(void)
{
    sampling_active = 1;
    sample_counter = 0;     // 重置采样计数器
    led_blink_counter = 0;  // 重置LED闪烁计数器
    // ...
}
```

### 时间戳生成机制
```c
// 获取当前时间字符串
void get_current_time_string(char* time_str)
{
    get_current_time();     // 从RTC读取当前时间
    sprintf(time_str, "20%02d-%02d-%02d %02d:%02d:%02d",
            current_time.year, current_time.month, current_time.date,
            current_time.hour, current_time.minute, current_time.second);
}
```

### 电压数据模拟
```c
// 模拟电压在10.4V-10.6V之间变化
float read_adc_voltage(void)
{
    static float voltage = 10.5f;
    static uint32_t counter = 0;
    
    counter++;
    if(counter % 10 == 0) {
        voltage = 10.5f + ((counter / 10) % 3 - 1) * 0.1f;
        // 输出序列：10.4V, 10.5V, 10.6V, 10.4V, 10.5V, 10.6V...
    }
    return voltage;
}
```

## 性能指标

### 时序精度
- **理论精度：** ±1ms
- **实际精度：** 取决于主循环执行时间和系统负载
- **累积误差：** 每小时 < 1秒（基于RTC硬件精度）

### 资源占用
- **CPU占用：** 极低（简单计数器操作）
- **内存占用：** 4字节计数器变量
- **中断依赖：** 无（基于轮询机制）

## 故障排除

### 问题1：采样间隔不准确
**可能原因：**
- 主循环执行时间过长
- 系统时钟配置错误
- RTC时钟源不稳定

**解决方案：**
- 优化主循环代码
- 检查系统时钟配置
- 验证RTC时钟源状态

### 问题2：时间戳不连续
**可能原因：**
- RTC时间未正确设置
- RTC寄存器读取错误
- 时间格式转换问题

**解决方案：**
- 使用`RTC Config`重新设置时间
- 检查RTC状态：`RTC status`
- 验证BCD转换逻辑

### 问题3：采样数据丢失
**可能原因：**
- 串口缓冲区溢出
- 主循环被阻塞
- 采样函数执行时间过长

**解决方案：**
- 增加串口缓冲区
- 优化阻塞操作
- 简化采样处理逻辑

## 验收标准

### 必须满足的要求
1. **精确5秒间隔** - 连续采样时间戳间隔为5秒
2. **时间同步** - 时间戳与RTC时间一致
3. **格式正确** - 输出格式严格按照规格
4. **长期稳定** - 连续运行1小时无异常

### 可选优化项目
1. **亚秒级精度** - 采样精度提升到毫秒级
2. **自适应周期** - 支持可配置的采样周期
3. **缓冲机制** - 采样数据缓冲和批量输出

---
**当前状态：** ✅ 5秒采样周期已正确实现
**验证结果：** 🟢 理论验证通过，等待硬件测试确认
