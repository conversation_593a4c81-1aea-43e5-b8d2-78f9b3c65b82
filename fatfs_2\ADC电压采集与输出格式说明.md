# ADC电压采集与输出格式说明

## 问题修正

根据您的反馈，我已经修正了以下问题：

### 1. 电压采集方式
**之前：** 使用模拟数据
**现在：** 使用真正的ADC硬件采集

### 2. 电压格式
**之前：** `ch0=10.5V`（一位小数）
**现在：** `ch0=10.50V`（严格保留小数点后两位）

## ADC硬件配置

### ADC通道配置
- **ADC外设：** ADC0
- **采样通道：** ADC_CHANNEL_10 (PC0引脚)
- **采样时间：** 56个ADC时钟周期
- **数据对齐：** 右对齐
- **转换模式：** 连续转换模式

### 电压转换计算
```c
float ADC_Read_Voltage(void)
{
    uint16_t adc_value = ADC_Read_Value();
    
    // ADC参考电压3.3V，12位ADC，最大值4095
    float voltage = (float)adc_value * 3.3f / 4095.0f;
    
    // 根据实际分压电路调整（需要根据硬件调整）
    voltage = voltage * 4.0f; // 假设4倍分压
    
    return voltage;
}
```

## 串口输出格式

### start命令完整输出格式
```
输入: start

输出:
Periodic Sampling
sample cycle: 5s
2025-01-01 00:30:05 ch0=10.50V
2025-01-01 00:30:10 ch0=10.50V
2025-01-01 00:30:15 ch0=10.51V
2025-01-01 00:30:20 ch0=10.49V
```

### stop命令输出格式
```
输入: stop

输出:
Periodic Sampling STOP
```

### 关键格式要求
1. **时间戳格式：** `2025-01-01 00:30:05`（完整日期时间）
2. **电压格式：** `ch0=10.50V`（通道+电压值，保留两位小数）
3. **周期显示：** `sample cycle: 5s`
4. **启动信息：** `Periodic Sampling`
5. **停止信息：** `Periodic Sampling STOP`

## 技术实现

### ADC读取流程
```c
uint16_t ADC_Read_Value(void)
{
    // 1. 启动ADC转换
    adc_software_trigger_enable(ADC0, ADC_ROUTINE_CHANNEL);
    
    // 2. 等待转换完成
    while(!adc_flag_get(ADC0, ADC_FLAG_EOC));
    
    // 3. 读取转换结果
    uint16_t adc_value = adc_routine_data_read(ADC0);
    
    // 4. 清除转换完成标志
    adc_flag_clear(ADC0, ADC_FLAG_EOC);
    
    return adc_value;
}
```

### 采样输出流程
```c
// 在update_sampling()函数中
if(sample_counter >= (sample_cycle * 1000)) {
    // 读取真实ADC电压值
    float voltage = read_adc_voltage(); // 调用ADC_Read_Voltage()
    
    // 获取当前时间
    char time_str[32];
    get_current_time_string(time_str);
    
    // 输出采样数据（保留小数点后两位）
    printf("%s ch0=%.2fV\r\n", time_str, voltage);
    
    sample_counter = 0;
}
```

## 硬件连接要求

### ADC输入引脚
- **引脚：** PC0 (ADC0_IN10)
- **模式：** 模拟输入模式
- **上拉/下拉：** 无

### 分压电路（如需要）
如果测量电压超过3.3V，需要外部分压电路：
```
输入电压 ----[R1]----+----[R2]---- GND
                     |
                   PC0 (ADC输入)
```

分压比计算：`Vin_max = 3.3V * (R1 + R2) / R2`

## 系统集成

### 初始化顺序
```c
void UsrFunction(void)
{
    LED_Init();
    gd_eval_com_init();
    spi_flash_init();
    
    // 初始化ADC
    ADC_port_init();  // 新增ADC初始化
    
    // 初始化RTC
    local_rtc_init();
    
    // 其他初始化...
}
```

### 采样控制集成
- **LED指示：** 1秒周期闪烁
- **OLED显示：** 时间(hh:mm:ss) + 电压(xx.xx V)
- **串口输出：** 5秒周期，格式严格按规格
- **时间戳：** 使用真实RTC时间

## 测试验证

### 基本功能测试
```bash
# 1. 启动采样
start
# 预期输出：
# Periodic Sampling
# sample cycle: 5s
# 2025-01-01 12:00:05 ch0=10.50V

# 2. 停止采样
stop
# 预期输出：
# Periodic Sampling STOP
```

### ADC精度验证
1. **输入已知电压** - 使用标准电压源
2. **对比读数** - 验证ADC读取精度
3. **校准分压比** - 调整voltage计算公式中的倍数

### 长期稳定性测试
1. **连续采样1小时** - 验证ADC稳定性
2. **温度变化测试** - 验证温度补偿
3. **电源变化测试** - 验证电源抑制比

## 注意事项

### ADC使用要点
1. **参考电压稳定性** - 确保3.3V电源稳定
2. **输入阻抗** - ADC输入阻抗较高，注意信号源阻抗
3. **噪声抑制** - 必要时添加滤波电路
4. **采样时间** - 根据信号源阻抗调整采样时间

### 分压电路设计
1. **精度要求** - 使用精密电阻
2. **温度系数** - 选择低温度系数电阻
3. **功耗考虑** - 平衡精度和功耗
4. **保护措施** - 添加过压保护

## 编译状态
✅ **编译成功** - 0个错误
- ADC驱动已集成
- 真实电压采集已实现
- 输出格式已修正

---
**修正完成时间：** 2025-06-16
**状态：** 🟢 ADC电压采集就绪，输出格式符合规格
