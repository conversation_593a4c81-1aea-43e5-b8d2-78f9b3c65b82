#include "gd32f4xx.h"
#include "LED.h"
#include "RTC.h"
#include "USART.h"
#include "SPI_FLASH.h"
#include "ff.h"
#include "diskio.h"
#include "sdcard.h"
// OLED函数声明（外部定义）
extern void OLED_Init(void);
extern void OLED_Clear(void);
extern void OLED_ShowString(unsigned char x, unsigned char y, unsigned char *chr, unsigned char size1);
extern void OLED_Refresh(void);
#include "ADC.h"
#include "key.h"  // 添加按键头文件
#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include <stdint.h>

/************************* �궨�� *************************/
#define  SFLASH_ID                     0xC84013
#define BUFFER_SIZE                    256
#define TX_BUFFER_SIZE                 BUFFER_SIZE
#define RX_BUFFER_SIZE                 BUFFER_SIZE
#define  FLASH_WRITE_ADDRESS           0x000000
#define  FLASH_READ_ADDRESS            FLASH_WRITE_ADDRESS
#define  CONFIG_FLASH_ADDRESS          0x001000  // 配置数据存储地址
#define  DEVICE_ID_FLASH_ADDRESS       0x002000  // 设备ID存储地址

/************************ �������� ************************/
uint32_t flash_id = 0;
uint8_t  tx_buffer[TX_BUFFER_SIZE];
uint8_t  rx_buffer[TX_BUFFER_SIZE];
uint16_t i = 0, count, result = 0;
uint8_t  is_successful = 0;

// 配置数据结构
typedef struct {
    float ratio;        // 变比
    float limit;        // 阈值
    uint32_t cycle;     // 采样周期（秒）
    uint32_t boot_count; // 上电次数
} config_data_t;

// 文件管理结构
typedef struct {
    uint8_t sample_count;      // sample文件当前数据条数
    uint8_t overlimit_count;   // overLimit文件当前数据条数
    uint8_t hide_count;        // hideData文件当前数据条数
    FIL sample_file;           // sample文件句柄
    FIL overlimit_file;        // overLimit文件句柄
    FIL hide_file;             // hideData文件句柄
    FIL log_file;              // log文件句柄
    uint8_t storage_enabled;   // 存储功能启用标志
} file_manager_t;

config_data_t config_data = {1.0, 1.0, 5, 0}; // 配置数据，默认变比1.0，阈值1.0，周期5秒，上电次数0
file_manager_t file_manager = {0}; // 文件管理器，初始化为0

// 设备ID字符串
const char device_id_string[] = "Device_ID:2025-CIMC-2025247961";

// 采样控制相关变量
uint8_t sampling_active = 0;     // 采样状态：0=停止，1=运行
uint32_t sample_cycle = 5;       // 采样周期（秒）
uint32_t led_blink_counter = 0;  // LED闪烁计数器
uint8_t led_state = 0;           // LED状态
uint8_t last_sample_second = 0;  // 上次采样的秒数（用于RTC时间间隔判断）
uint8_t hide_mode = 0;           // 隐藏模式：0=正常显示，1=HEX格式显示

// 命令匹配函数声明
uint8_t check_command_match(char* buffer, uint8_t length);

// 按键控制相关变量
uint8_t key1_last_state = 0;     // KEY1上次状态
uint8_t key2_last_state = 0;     // KEY2上次状态
uint8_t key3_last_state = 0;     // KEY3上次状态
uint8_t key4_last_state = 0;     // KEY4上次状态
uint32_t key_debounce_counter = 0; // 按键防抖计数器
uint8_t key1_pressed_flag = 0;    // KEY1按下标志
uint8_t key2_pressed_flag = 0;    // KEY2按下标志
uint8_t key3_pressed_flag = 0;    // KEY3按下标志
uint8_t key4_pressed_flag = 0;    // KEY4按下标志
uint32_t key_check_counter = 0;   // 按键检测计数器

FIL fdst;
FATFS fs;
UINT br, bw;
//BYTE textfilebuffer[2048] = "GD32MCU FATFS TEST!\r\n";
BYTE buffer[128];
BYTE filebuffer[128];

// RTC参数结构（本地定义）
typedef struct {
    uint8_t year;
    uint8_t month;
    uint8_t date;
    uint8_t hour;
    uint8_t minute;
    uint8_t second;
} rtc_time_struct;

rtc_time_struct current_time;

/************************ �������� ************************/
ErrStatus memory_compare(uint8_t* src,uint8_t* dst,uint16_t length);
void nvic_config(void);
void write_file(void);
void system_self_check(void); // ϵͳ�Լ�����
void process_command(char* cmd); // ����������
void flash_self_check(void); // FLASH�Լ�����
void flash_test_write_read(void); // FLASH��д����
void read_config_from_tf(void); // 从TF卡读取配置
void save_config_to_flash(void); // 保存配置到Flash
void load_config_from_flash(void); // 从Flash加载配置
void set_ratio_value(void); // 设置变比值
void set_limit_value(void); // 设置阈值
void test_parameter_protection(void); // 测试参数保护功能
void config_save_command(void); // 保存参数到Flash命令
void config_read_command(void); // 从Flash读取参数命令
void start_sampling(void); // 启动采样
void stop_sampling(void); // 停止采样
void update_sampling(void); // 更新采样状态
float read_adc_voltage(void); // 读取ADC电压值
void update_oled_display(void); // 更新OLED显示
void get_current_time_string(char* time_str); // 获取当前时间字符串
void get_current_time(void); // 获取当前时间
void rtc_config_command(void); // RTC时间配置命令
void rtc_now_command(void); // RTC当前时间显示命令
uint8_t parse_time_input(char* input, rtc_time_struct* time_data); // 解析时间输入
uint8_t convert_to_bcd(uint8_t decimal); // 十进制转BCD
void local_rtc_init(void); // 本地RTC初始化函数
void rtc_status_check(void); // RTC状态检查函数
void rtc_reset_command(void); // RTC重置命令
void check_key1_press(void); // 检查KEY1按键状态
void check_key234_press(void); // 检查KEY2/3/4按键状态
void toggle_sampling_by_key(void); // 通过按键切换采样状态
void adjust_sample_cycle(uint32_t new_cycle); // 调整采样周期
uint32_t get_unix_timestamp(void); // 获取Unix时间戳
void encode_voltage_hex(float voltage, char* hex_str); // 电压值HEX编码
void hide_command(void); // 隐藏模式命令
void unhide_command(void); // 取消隐藏模式命令
void increment_boot_count(void); // 增加上电次数并保存到Flash
void get_datetime_string(char* datetime_str); // 获取14位数字时间格式
void generate_sample_filename(char* filename); // 生成sample文件名
void generate_overlimit_filename(char* filename); // 生成overLimit文件名
void generate_hide_filename(char* filename); // 生成hideData文件名
void generate_log_filename(char* filename); // 生成log文件名
void create_directories(void); // 创建存储目录
void init_data_storage(void); // 初始化数据存储
void write_sample_data(char* data_str, uint8_t is_hide_mode); // 写入采样数据
void check_and_create_new_file(uint8_t file_type); // 检查并创建新文件
void write_overlimit_data(char* data_str, float voltage, float limit); // 写入超限数据
void write_log_data(char* operation); // 写入日志数据
void save_device_id_to_flash(void); // 保存设备ID到Flash
void read_device_id_from_flash(void); // 从Flash读取设备ID

/************************************************************ 
 * Function :       System_Init
 * Comment  :       ���ڳ�ʼ��MCU
 * Parameter:       null
 * Return   :       null
 * Author   :       Lingyu Meng
 * Date     :       2025-02-30 V0.1 original
************************************************************/

void System_Init(void)
{
	systick_config();     // 时钟配置

	// 初始化串口（需要在打印之前初始化）
	rcu_periph_clock_enable(RCU_GPIOA);
	rcu_periph_clock_enable(RCU_USART0);

	// 配置USART0引脚
	gpio_af_set(GPIOA, GPIO_AF_7, GPIO_PIN_9);
	gpio_af_set(GPIOA, GPIO_AF_7, GPIO_PIN_10);
	gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_9);
	gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_9);
	gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_10);
	gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_10);

	// 配置USART0
	usart_deinit(USART0);
	usart_baudrate_set(USART0, 115200U);
	usart_receive_config(USART0, USART_RECEIVE_ENABLE);
	usart_transmit_config(USART0, USART_TRANSMIT_ENABLE);
	usart_enable(USART0);

	// 系统初始化开始提示
	printf("====system init====\r\n");

	// 初始化SPI Flash（需要在读取设备ID之前）
	spi_flash_init();

	// 从Flash读取设备ID
	read_device_id_from_flash();
}



/************************************************************ 
 * Function :       UsrFunction
 * Comment  :       �û�������
 * Parameter:       null
 * Return   :       null
 * Author   :       Liu Tao @ GigaDevice
 * Date     :       2025-05-10 V0.1 original
************************************************************/

void UsrFunction(void)
{
	uint16_t k = 5;
	DSTATUS stat = 0;
	nvic_config();

	LED_Init();
	gd_eval_com_init();

	// 初始化ADC
	ADC_port_init();

	// 初始化按键
	KEY_Init();

	// 初始化OLED
	OLED_Init();
	OLED_Clear();
	OLED_ShowString(0, 0, (unsigned char*)"system idle", 12);
	OLED_Refresh();

	// 初始化RTC
	local_rtc_init();

	// 从Flash加载配置数据
	load_config_from_flash();

	// 增加上电次数
	increment_boot_count();

	// 同步采样周期到全局变量
	sample_cycle = config_data.cycle;

	do
	{
		stat = disk_initialize(0);
	}while((stat != 0) && (--k));
    
    // printf("SD Card disk_initialize:%d\r\n",stat);
    f_mount(0, &fs);
    // printf("SD Card f_mount:%d\r\n",stat);

	if(RES_OK == stat)
	{
        // printf("\r\nSD Card Initialize Success!\r\n");
        // 初始化数据存储
        init_data_storage();
	}

	// 系统就绪提示
	printf("====system ready====\r\n");
	printf("System ready. Enter commands:\r\n");
	printf("- 'test': System self-check (full string required)\r\n");
	printf("- 'conf': Read config from TF card\r\n");
	printf("- 'ratio': Set ratio value (0~100)\r\n");
	printf("- 'limit': Set limit value (0~500)\r\n");
	printf("- 'config save': Save parameters to flash\r\n");
	printf("- 'config read': Read parameters from flash\r\n");
	printf("- 'start' or 's': Start periodic sampling (full string required)\r\n");
	printf("- 'stop' or 's': Stop periodic sampling (full string required)\r\n");
	printf("- 'hide' or 'h': Convert data to HEX format (full string, only during sampling)\r\n");
	printf("- 'unhide' or 'u': Restore normal format (full string, only during sampling)\r\n");
	printf("- 'KEY1': Press to toggle sampling (start/stop)\r\n");
	printf("- 'RTC Config': Set RTC time\r\n");
	printf("- 'RTC now': Show current time\r\n");
	printf("- 'RTC status': Show RTC debug info\r\n");
	printf("- 'RTC reset': Reset RTC domain\r\n");
	printf("- 'rtctest': Simple RTC test (set fixed time)\r\n");
	printf("- 'tragll': Write file test\r\n");
	printf("- '?': Quick self-check\r\n");

	char cmd_buffer[32] = {0}; // 命令缓冲区，增大到32字节以支持长命令
	uint8_t cmd_index = 0;
	uint32_t led_counter = 0; // LED��������

	printf("Enter command: ");

	while(1)
	{
		if(usart_flag_get(USART0, USART_FLAG_RBNE) != RESET){
			char ch = usart_data_receive(USART0);

			// 处理回车换行 - 执行命令
			if(ch == '\r' || ch == '\n'){
				if(cmd_index > 0){
					cmd_buffer[cmd_index] = '\0';

					// 使用新的命令匹配函数
					if(!check_command_match(cmd_buffer, cmd_index)) {
						// 如果没有匹配到快速命令，使用原有的处理方式
						process_command(cmd_buffer);
					}

					// 完全清零缓冲区
					cmd_index = 0;
					memset(cmd_buffer, 0, sizeof(cmd_buffer));
					printf("\r\nEnter command: ");
				}
			}
			// 处理单字符命令（只有在缓冲区为空时才执行）
			else if(cmd_index == 0 && (ch == '?' || ch == 't' || ch == 'T' || ch == 'w' || ch == 'W' || ch == 'f' || ch == 'F' || ch == 'h' || ch == 'H' || ch == 'u' || ch == 'U' || ch == 's' || ch == 'S')) {
				if(ch == '?') {
					system_self_check();
				}
				else if(ch == 't' || ch == 'T') {
					system_self_check();
				}
				else if(ch == 'h' || ch == 'H') {
					hide_command();
				}
				else if(ch == 'u' || ch == 'U') {
					unhide_command();
				}
				else if(ch == 's' || ch == 'S') {
					if(sampling_active) {
						stop_sampling();
					} else {
						start_sampling();
					}
				}
				else if(ch == 'w' || ch == 'W') {
					// 执行文件写入测试
					FRESULT result = f_open(&fdst, "0:/FATFS.TXT", FA_CREATE_ALWAYS | FA_WRITE);
					if(result == FR_OK) {
						write_file();
						result = f_write(&fdst, filebuffer, strlen((char*)filebuffer), &bw);
						if(FR_OK == result) {
							printf("FATFS FILE write Success!\r\n");
						} else {
							printf("FATFS FILE write failed!\r\n");
						}
						f_close(&fdst);

						// 读取文件进行验证
						result = f_open(&fdst, "0:/FATFS.TXT", FA_OPEN_EXISTING | FA_READ);
						if(result == FR_OK) {
							memset(buffer, 0, sizeof(buffer));
							result = f_read(&fdst, buffer, sizeof(buffer), &br);
							if(result == FR_OK && br > 0) {
								if(SUCCESS == memory_compare(buffer, filebuffer, strlen((char*)filebuffer))) {
									printf("FATFS Read File Success!\r\nThe content is:%s\r\n", buffer);
								} else {
									printf("FATFS FILE read failed!\r\n");
								}
							}
							f_close(&fdst);
						}
					} else {
						printf("Failed to open file for writing!\r\n");
					}
				}
				else if(ch == 'f' || ch == 'F') {
					// 执行FLASH测试
					flash_self_check();
				}
				// 清零缓冲区
				cmd_index = 0;
				memset(cmd_buffer, 0, sizeof(cmd_buffer));
				printf("\r\nEnter command: ");
			}
			// 处理多字符命令 - 将字符加入缓冲区
			else if(cmd_index < sizeof(cmd_buffer)-1){
				cmd_buffer[cmd_index++] = ch;
				printf("%c", ch); // 回显字符
			}
			else {
				// 缓冲区溢出，重置
				cmd_index = 0;
				memset(cmd_buffer, 0, sizeof(cmd_buffer));
				printf("\r\nCommand too long. Enter command: ");
			}
		}

		// 检查按键状态（降低检测频率）
		key_check_counter++;
		if(key_check_counter >= 1000) { // 每1000次循环检测一次按键（约1ms间隔）
			check_key1_press();
			check_key234_press();
			key_check_counter = 0;
		}

		// 更新采样状态
		update_sampling();

		led_counter++;
		if(led_counter >= 50000) { // ����ӳ�
			gpio_bit_toggle(GPIOA, GPIO_PIN_5);
			led_counter = 0;
		}
	}
}


void nvic_config(void)
{
    nvic_priority_group_set(NVIC_PRIGROUP_PRE1_SUB3);	// �����ж����ȼ�����
    nvic_irq_enable(SDIO_IRQn, 0, 0);					// ʹ��SDIO�жϣ����ȼ�Ϊ0
}

void write_file(void)
{
    printf("Input data (press Enter to save):\r\n");

    uint16_t index = 0;
    while(1){
        if(usart_flag_get(USART0, USART_FLAG_RBNE) != RESET){
            char ch = usart_data_receive(USART0); 						// ������ջ������ǿգ���USART0����һ���ַ�
            if(ch == '\r'){												// �����յ����ַ��Ƿ�Ϊ�س�����'\r'��
                filebuffer[index] = '\0';  								// ����ǻس������ڵ�ǰλ�������ַ��������� '\0'
                break;													// ����ѭ�����������ݽ���
            }
            filebuffer[index++] = ch;        							// �洢���յ����ַ�
            if(index >= sizeof(filebuffer)-1) break;					// ��������������������
        }
    }
}

/* ϵͳ�Լ����� */
void system_self_check(void)
{
    printf("====== system selftest ======\r\n");

    // ���FLASH״̬
    flash_id = spi_flash_read_id();
    if(SFLASH_ID == flash_id) {
        printf("flash ............ ok\r\n");
    } else {
        printf("flash ............ error\r\n");
    }

    // ���TF��״̬
    DSTATUS tf_status = disk_initialize(0);
    if(tf_status == RES_OK) {
        printf("TF card ............ ok\r\n");
    } else {
        printf("TF card............ error\r\n");
    }

    // ��ʾFLASH ID
    printf("flash ID: 0x%06X\r\n", flash_id);

    // ��ʾTF������Ϣ
    if(tf_status == RES_OK) {
        FATFS *fs_ptr;
        DWORD free_clusters;
        FRESULT res = f_getfree("0:", &free_clusters, &fs_ptr);

        if(res == FR_OK) {
            DWORD total_sectors = (fs_ptr->n_fatent - 2) * fs_ptr->csize;
            DWORD total_kb = total_sectors / 2; 
            printf("TF card memory: %lu KB\r\n", total_kb);
        } else {
            printf("TF card memory: Unable to get size\r\n");
        }
    } else {
        printf("can not find TF card\r\n");
    }

    printf("====== system selftest ======\r\n");
}

void process_command(char* cmd)
{
    printf("Debug: Processing command: '%s' (length: %d)\r\n", cmd, strlen(cmd));

    if(strcmp(cmd, "test") == 0) {
        system_self_check();
    }
    else if(strcmp(cmd, "conf") == 0) {
        read_config_from_tf();
    }
    else if(strcmp(cmd, "ratio") == 0) {
        set_ratio_value();
    }
    else if(strcmp(cmd, "limit") == 0) {
        set_limit_value();
    }
    else if(strcmp(cmd, "config save") == 0) {
        config_save_command();
    }
    else if(strcmp(cmd, "config read") == 0) {
        config_read_command();
    }
    else if(strcmp(cmd, "start") == 0) {
        start_sampling();
    }
    else if(strcmp(cmd, "stop") == 0) {
        stop_sampling();
    }
    else if(strcmp(cmd, "hide") == 0) {
        hide_command();
    }
    else if(strcmp(cmd, "unhide") == 0) {
        unhide_command();
    }
    else if(strcmp(cmd, "RTC Config") == 0) {
        printf("Debug: RTC Config command matched\r\n");
        rtc_config_command();
    }
    else if(strcmp(cmd, "RTC now") == 0) {
        rtc_now_command();
    }
    else if(strcmp(cmd, "RTC status") == 0) {
        rtc_status_check();
    }
    else if(strcmp(cmd, "RTC reset") == 0) {
        rtc_reset_command();
    }
    else if(strcmp(cmd, "rtctest") == 0) {
        printf("RTC Test: Simple time setting\r\n");
        printf("Setting time to 2025-01-15 14:30:00\r\n");

        // 直接设置一个固定时间进行测试
        rtc_parameter_struct rtc_initpara;
        rtc_initpara.factor_asyn = 0x7F;
        rtc_initpara.factor_syn = 0xFF;
        rtc_initpara.year = convert_to_bcd(25);      // 2025
        rtc_initpara.month = convert_to_bcd(1);      // 1月
        rtc_initpara.date = convert_to_bcd(15);      // 15日
        rtc_initpara.hour = convert_to_bcd(14);      // 14时
        rtc_initpara.minute = convert_to_bcd(30);    // 30分
        rtc_initpara.second = convert_to_bcd(0);     // 0秒
        rtc_initpara.display_format = RTC_24HOUR;
        rtc_initpara.am_pm = RTC_AM;
        rtc_initpara.day_of_week = RTC_MONDAY;

        if(rtc_init(&rtc_initpara) == SUCCESS) {
            printf("RTC Test: Time set successfully\r\n");
            RTC_BKP0 = 0x32F0;
        } else {
            printf("RTC Test: Time set failed\r\n");
        }
    }
    else if(strcmp(cmd, "tragll") == 0) {
        FRESULT result = f_open(&fdst, "0:/FATFS.TXT", FA_CREATE_ALWAYS | FA_WRITE);
        if(result == FR_OK) {
            write_file();
            result = f_write(&fdst, filebuffer, strlen((char*)filebuffer), &bw);

            if(FR_OK == result) {
                printf("FATFS FILE write Success!\r\n");
            } else {
                printf("FATFS FILE write failed!\r\n");
            }
            f_close(&fdst);


            result = f_open(&fdst, "0:/FATFS.TXT", FA_OPEN_EXISTING | FA_READ);
            if(result == FR_OK) {
                memset(buffer, 0, sizeof(buffer));
                result = f_read(&fdst, buffer, sizeof(buffer), &br);
                if(result == FR_OK && br > 0) {
                    if(SUCCESS == memory_compare(buffer, filebuffer, strlen((char*)filebuffer))) {
                        printf("FATFS Read File Success!\r\nThe content is:%s\r\n", buffer);
                    } else {
                        printf("FATFS FILE read failed!\r\n");
                    }
                }
                f_close(&fdst);
            }
        } else {
            printf("Failed to open file for writing!\r\n");
        }
    }
    else {
        printf("Unknown command: '%s'\r\n", cmd);
        printf("Available commands: 'test', 'conf', 'ratio', 'limit', 'config save', 'config read', 'start', 'stop', 'hide', 'unhide', 'RTC Config', 'RTC now', 'rtctest', 'tragll'\r\n");
    }
}
/*!
    \brief      memory compare function
    \param[in]  src: source data pointer
    \param[in]  dst: destination data pointer
    \param[in]  length: the compare data length
    \param[out] none
    \retval     ErrStatus: ERROR or SUCCESS
*/
ErrStatus memory_compare(uint8_t* src, uint8_t* dst, uint16_t length)
{
    while(length --){
        if(*src++ != *dst++)
            return ERROR;
    }
    return SUCCESS;
}

void flash_self_check(void)
{
    flash_id = spi_flash_read_id();

    if(SFLASH_ID == flash_id) {
        printf("flash ............ ok\r\n");
        printf("flash ID: 0x%06X\r\n", flash_id);

        flash_test_write_read();
    } else {
        printf("flash ............ error\r\n");
        printf("flash ID: 0x%06X\r\n", flash_id);
    }
}


void flash_test_write_read(void)
{
    for(i = 0; i < BUFFER_SIZE; i++) {
        tx_buffer[i] = i;
        rx_buffer[i] = 0;
    }

    spi_flash_sector_erase(FLASH_WRITE_ADDRESS);

    spi_flash_buffer_write(tx_buffer, FLASH_WRITE_ADDRESS, BUFFER_SIZE);

    spi_flash_buffer_read(rx_buffer, FLASH_READ_ADDRESS, BUFFER_SIZE);


    if(SUCCESS == memory_compare(tx_buffer, rx_buffer, BUFFER_SIZE)) {
        printf("flash R/W ............ ok\r\n");
        is_successful = 1;
    } else {
        printf("flash R/W ............ error\r\n");
        is_successful = 0;
    }
}

// 从TF卡读取配置文件
void read_config_from_tf(void)
{
    FRESULT result = f_open(&fdst, "0:/config.ini", FA_OPEN_EXISTING | FA_READ);

    if(result != FR_OK) {
        printf("config.ini file not found.\r\n");
        return;
    }

    char file_content[256];
    UINT bytes_read;

    // 读取整个文件内容
    result = f_read(&fdst, file_content, sizeof(file_content)-1, &bytes_read);
    f_close(&fdst);

    if(result != FR_OK || bytes_read == 0) {
        printf("config.ini file read error\r\n");
        return;
    }

    file_content[bytes_read] = '\0'; // 确保字符串结束

    // 调试：显示读取的文件内容
    printf("File content (%d bytes):\r\n", bytes_read);
    for(int i = 0; i < bytes_read; i++) {
        if(file_content[i] >= 32 && file_content[i] <= 126) {
            printf("%c", file_content[i]);
        } else {
            printf("[%02X]", file_content[i]);
        }
    }
    printf("\r\n");

    uint32_t ratio_found = 0, limit_found = 0;

    // 简化解析：直接在整个文件内容中查找
    char *ratio_pos = strstr(file_content, "Ratio=");
    char *limit_pos = strstr(file_content, "Limit=");

    if(ratio_pos != NULL) {
        config_data.ratio = atof(ratio_pos + 6);
        ratio_found = 1;
        printf("Found Ratio: %.1f\r\n", config_data.ratio);
    }

    if(limit_pos != NULL) {
        config_data.limit = atof(limit_pos + 6);
        limit_found = 1;
        printf("Found Limit: %.2f\r\n", config_data.limit);
    }

    if(ratio_found && limit_found) {
        printf("Ratio=%.1f\r\n", config_data.ratio);
        printf("Limit = %.2f\r\n", config_data.limit);
        printf("config read success\r\n");

        // 保存到Flash
        save_config_to_flash();
    } else {
        printf("config.ini format error\r\n");
        printf("ratio_found=%d, limit_found=%d\r\n", ratio_found, limit_found);
    }
}

// 保存配置到Flash
void save_config_to_flash(void)
{
    spi_flash_sector_erase(CONFIG_FLASH_ADDRESS);
    spi_flash_buffer_write((uint8_t*)&config_data, CONFIG_FLASH_ADDRESS, sizeof(config_data_t));
}

// 从Flash加载配置
void load_config_from_flash(void)
{
    config_data_t temp_data;
    spi_flash_buffer_read((uint8_t*)&temp_data, CONFIG_FLASH_ADDRESS, sizeof(config_data_t));

    // 验证读取的数据是否有效
    if(temp_data.ratio >= 0.0 && temp_data.ratio <= 100.0 &&
       temp_data.limit >= 0.0 && temp_data.limit <= 500.0 &&
       (temp_data.cycle == 5 || temp_data.cycle == 10 || temp_data.cycle == 15)) {
        // 数据有效，更新配置
        config_data = temp_data;
        sample_cycle = config_data.cycle; // 同步采样周期
        // 验证boot_count有效性，如果无效则重置为0
        if(config_data.boot_count > 999999) { // 防止异常大值
            config_data.boot_count = 0;
        }
    } else {
        // 数据无效，使用默认值
        config_data.ratio = 1.0;
        config_data.limit = 1.0;
        config_data.cycle = 5;
        config_data.boot_count = 0;
        sample_cycle = 5;
        // printf("Flash data invalid, using default values\r\n");
    }
}

// 设置变比值
void set_ratio_value(void)
{
    // 显示当前变比值
    printf("Ratio=%.1f\r\n", config_data.ratio);
    printf("Input value(0~100):\r\n");

    char input_buffer[16] = {0};
    uint8_t input_index = 0;

    // 读取用户输入
    while(1) {
        if(usart_flag_get(USART0, USART_FLAG_RBNE) != RESET) {
            char ch = usart_data_receive(USART0);

            if(ch == '\r' || ch == '\n') {
                if(input_index > 0) {
                    input_buffer[input_index] = '\0';
                    break;
                }
            }
            else if(input_index < sizeof(input_buffer)-1) {
                input_buffer[input_index++] = ch;
                printf("%c", ch); // 回显字符
            }
        }
    }

    // 解析输入值
    float new_ratio = atof(input_buffer);

    // 验证输入有效性 - 检查是否为有效数字且在范围内
    // atof对于无效输入返回0.0，所以需要特别检查
    uint8_t is_valid_number = 0;
    if(strlen(input_buffer) > 0) {
        // 检查是否包含有效的数字字符
        for(int i = 0; i < strlen(input_buffer); i++) {
            if((input_buffer[i] >= '0' && input_buffer[i] <= '9') ||
               input_buffer[i] == '.' || input_buffer[i] == '-') {
                is_valid_number = 1;
                break;
            }
        }
    }

    if(is_valid_number && new_ratio >= 0.0 && new_ratio <= 100.0) {
        config_data.ratio = new_ratio;
        printf("\r\nratio modified success\r\n");
        printf("Ratio = %.1f\r\n", config_data.ratio);

        // 保存到Flash
        save_config_to_flash();
    } else {
        printf("\r\nratio invalid\r\n");
        printf("Ratio = %.1f\r\n", config_data.ratio);
    }
}

// 设置阈值
void set_limit_value(void)
{
    // 显示当前阈值
    printf("limit=%.1f\r\n", config_data.limit);
    printf("Input value(0~500):\r\n");

    char input_buffer[16] = {0};
    uint8_t input_index = 0;

    // 读取用户输入
    while(1) {
        if(usart_flag_get(USART0, USART_FLAG_RBNE) != RESET) {
            char ch = usart_data_receive(USART0);

            if(ch == '\r' || ch == '\n') {
                if(input_index > 0) {
                    input_buffer[input_index] = '\0';
                    break;
                }
            }
            else if(input_index < sizeof(input_buffer)-1) {
                input_buffer[input_index++] = ch;
                printf("%c", ch); // 回显字符
            }
        }
    }

    // 解析输入值
    float new_limit = atof(input_buffer);

    // 验证输入有效性 - 检查是否为有效数字且在范围内
    // atof对于无效输入返回0.0，所以需要特别检查
    uint8_t is_valid_number = 0;
    if(strlen(input_buffer) > 0) {
        // 检查是否包含有效的数字字符
        for(int i = 0; i < strlen(input_buffer); i++) {
            if((input_buffer[i] >= '0' && input_buffer[i] <= '9') ||
               input_buffer[i] == '.' || input_buffer[i] == '-') {
                is_valid_number = 1;
                break;
            }
        }
    }

    if(is_valid_number && new_limit >= 0.0 && new_limit <= 500.0) {
        config_data.limit = new_limit;
        printf("\r\nlimit modified success\r\n");
        printf("limit = %.2f\r\n", config_data.limit);

        // 保存到Flash
        save_config_to_flash();
    } else {
        printf("\r\nlimit invalid\r\n");
        printf("limit = %.1f\r\n", config_data.limit);
    }
}

// 测试参数保护功能
void test_parameter_protection(void)
{
    printf("=== Parameter Protection Test ===\r\n");

    // 保存原始值
    float original_ratio = config_data.ratio;
    float original_limit = config_data.limit;

    printf("Original values: Ratio=%.1f, Limit=%.1f\r\n", original_ratio, original_limit);

    // 测试无效输入不会修改参数
    printf("Testing invalid inputs...\r\n");

    // 这里只是演示逻辑，实际测试需要模拟用户输入
    printf("- Invalid ratio (>100): Parameters should remain unchanged\r\n");
    printf("- Invalid limit (>500): Parameters should remain unchanged\r\n");
    printf("- Non-numeric input: Parameters should remain unchanged\r\n");

    // 验证参数未被修改
    if(config_data.ratio == original_ratio && config_data.limit == original_limit) {
        printf("✓ Parameter protection working correctly\r\n");
    } else {
        printf("✗ Parameter protection failed!\r\n");
    }

    printf("Current values: Ratio=%.1f, Limit=%.1f\r\n", config_data.ratio, config_data.limit);
    printf("=== Test Complete ===\r\n");
}

// 保存参数到Flash命令
void config_save_command(void)
{
    // 显示当前参数
    printf("ratio: %.1f\r\n", config_data.ratio);
    printf("limit: %.2f\r\n", config_data.limit);

    // 保存到Flash
    save_config_to_flash();

    printf("save parameters to flash\r\n");
    write_log_data("Configuration saved to flash");
}

// 从Flash读取参数命令
void config_read_command(void)
{
    printf("read parameters from flash\r\n");

    // 从Flash加载参数
    load_config_from_flash();

    // 显示读取的参数
    printf("ratio: %.1f\r\n", config_data.ratio);
    printf("limit: %.2f\r\n", config_data.limit);
    printf("cycle: %lus\r\n", config_data.cycle);
    printf("boot_count: %lu\r\n", config_data.boot_count);
}

// 启动采样
void start_sampling(void)
{
    sampling_active = 1;
    led_blink_counter = 0;
    led_state = 0;
    last_sample_second = 255; // 初始化为无效值，确保第一次采样能正常执行

    printf("Periodic Sampling\r\n");
    printf("sample cycle: %lus\r\n", sample_cycle);

    // 记录日志
    write_log_data("Sampling started");

    // 立即更新OLED显示
    update_oled_display();
}

// 停止采样
void stop_sampling(void)
{
    sampling_active = 0;
    LED1_OFF(); // 关闭LED1
    LED2_OFF(); // 关闭LED2（清除超限指示）

    // 如果当前是HEX模式，提示恢复正常模式
    if(hide_mode != 0) {
        hide_mode = 0; // 恢复正常显示模式
        printf("Periodic Sampling STOP\r\n");
        printf("Data format restored to normal mode\r\n");
        write_log_data("Sampling stopped (hide mode disabled)");
    } else {
        printf("Periodic Sampling STOP\r\n");
        write_log_data("Sampling stopped");
    }

    // 更新OLED显示为空闲状态
    update_oled_display();
}

// 更新采样状态
void update_sampling(void)
{
    if(!sampling_active) return;

    // LED1闪烁控制（1秒周期）
    led_blink_counter++;
    if(led_blink_counter >= 1000) { // 假设1ms调用一次，1000次=1秒
        if(led_state) {
            LED1_OFF();
            led_state = 0;
        } else {
            LED1_ON();
            led_state = 1;
        }
        led_blink_counter = 0;
    }

    // 基于RTC真实时间的5秒间隔采样
    get_current_time(); // 获取当前RTC时间

    // 检查是否到了5秒间隔的采样时间点
    if(current_time.second % sample_cycle == 0 && current_time.second != last_sample_second) {
        // 读取电压值
        float voltage = read_adc_voltage();

        // 获取当前时间字符串
        char time_str[32];
        get_current_time_string(time_str);

        // 超限检测
        uint8_t is_overlimit = (voltage > config_data.limit);
        if(is_overlimit) {
            LED2_ON(); // 超限：点亮LED2
        } else {
            LED2_OFF(); // 正常：关闭LED2
        }

        // 根据显示模式输出数据
        if(hide_mode) {
            // HEX格式输出
            uint32_t timestamp = get_unix_timestamp();
            char voltage_hex[9]; // 8位HEX + 结束符
            encode_voltage_hex(voltage, voltage_hex);

            if(is_overlimit) {
                printf("=== >%08X%s*\r\n", timestamp, voltage_hex);
            } else {
                printf("=== >%08X%s\r\n", timestamp, voltage_hex);
            }
        } else {
            // 正常格式输出
            if(is_overlimit) {
                printf("=== >%s ch0=%.1fV OverLimit (%.2f)!\r\n", time_str, voltage, config_data.limit);
            } else {
                printf("%s ch0=%.1fV\r\n", time_str, voltage);
            }
        }

        // 文件存储功能（仅在存储功能启用时执行）
        if(file_manager.storage_enabled) {
            // 写入采样数据到文件
            char sample_data[64];
            if(hide_mode) {
                // HEX格式数据
                uint32_t timestamp = get_unix_timestamp();
                char voltage_hex[9];
                encode_voltage_hex(voltage, voltage_hex);
                if(is_overlimit) {
                    sprintf(sample_data, "=== >%08X%s*", timestamp, voltage_hex);
                } else {
                    sprintf(sample_data, "=== >%08X%s", timestamp, voltage_hex);
                }
                write_sample_data(sample_data, 1); // 写入hideData文件夹
            } else {
                // 正常格式数据
                sprintf(sample_data, "%s ch0=%.1fV", time_str, voltage);
                write_sample_data(sample_data, 0); // 写入sample文件夹
            }

            // 如果超限，写入超限数据（无论hide_mode状态如何）
            if(is_overlimit) {
                write_overlimit_data(time_str, voltage, config_data.limit);
            }
        }

        // 记录本次采样的秒数，避免重复采样
        last_sample_second = current_time.second;
    }

    // 更新OLED显示（每5秒更新一次，与采样周期同步）
    static uint32_t oled_update_counter = 0;
    oled_update_counter++;
    if(oled_update_counter >= 5000) { // 每5000次调用更新一次（约5秒）
        update_oled_display();
        oled_update_counter = 0;
    }
}

// 读取ADC电压值
float read_adc_voltage(void)
{
    // 调用真正的ADC读取函数
    return ADC_Read_Voltage();
}

// 更新OLED显示
void update_oled_display(void)
{
    char time_str[16];
    char voltage_str[16];

    if(sampling_active) {
        // 采样模式：显示时间和电压
        // 获取当前时间（只显示时分秒）
        get_current_time();
        sprintf(time_str, "%02d:%02d:%02d", current_time.hour, current_time.minute, current_time.second);

        // 获取电压值
         float voltage = read_adc_voltage();
         sprintf(voltage_str, "%.2f V", voltage);

         OLED_Clear();
         OLED_ShowString(0, 0, (unsigned char*)time_str, 12);
         OLED_ShowString(0, 16, (unsigned char*)voltage_str, 12);
         OLED_Refresh();
    } else {
          //空闲模式：显示"system idle"
        OLED_Clear();
        OLED_ShowString(0, 0, (unsigned char*)"system idle", 12);
        OLED_Refresh();
    }
}

// 获取当前时间字符串
void get_current_time_string(char* time_str)
{
    get_current_time();
    sprintf(time_str, "20%02d-%02d-%02d %02d:%02d:%02d",
            current_time.year, current_time.month, current_time.date,
            current_time.hour, current_time.minute, current_time.second);
}

// 获取当前时间（从RTC读取）
void get_current_time(void)
{
    rtc_parameter_struct rtc_time;
    rtc_current_time_get(&rtc_time);

    // 将BCD格式转换为十进制
    current_time.year = ((rtc_time.year >> 4) * 10) + (rtc_time.year & 0x0F);
    current_time.month = ((rtc_time.month >> 4) * 10) + (rtc_time.month & 0x0F);
    current_time.date = ((rtc_time.date >> 4) * 10) + (rtc_time.date & 0x0F);
    current_time.hour = ((rtc_time.hour >> 4) * 10) + (rtc_time.hour & 0x0F);
    current_time.minute = ((rtc_time.minute >> 4) * 10) + (rtc_time.minute & 0x0F);
    current_time.second = ((rtc_time.second >> 4) * 10) + (rtc_time.second & 0x0F);
}

// 十进制转BCD格式
uint8_t convert_to_bcd(uint8_t decimal)
{
    return ((decimal / 10) << 4) | (decimal % 10);
}

// 解析时间输入字符串
uint8_t parse_time_input(char* input, rtc_time_struct* time_data)
{
    int year, month, date, hour, minute, second;
    int parsed_count = 0;

    // 尝试多种时间格式解析
    // 格式1: 2025-01-01 12:00:30
    parsed_count = sscanf(input, "%d-%d-%d %d:%d:%d", &year, &month, &date, &hour, &minute, &second);

    if(parsed_count != 6) {
        // 格式2: 2025 01 01 12 00 30 (空格分隔)
        parsed_count = sscanf(input, "%d %d %d %d %d %d", &year, &month, &date, &hour, &minute, &second);
    }

    if(parsed_count != 6) {
        // 格式3: 2025/01/01 12:00:30
        parsed_count = sscanf(input, "%d/%d/%d %d:%d:%d", &year, &month, &date, &hour, &minute, &second);
    }

    if(parsed_count != 6) {
        // 格式4: 20250101120030 (纯数字)
        if(strlen(input) == 14) {
            char temp[3] = {0};
            strncpy(temp, input, 4); year = atoi(temp);
            strncpy(temp, input+4, 2); temp[2] = 0; month = atoi(temp);
            strncpy(temp, input+6, 2); temp[2] = 0; date = atoi(temp);
            strncpy(temp, input+8, 2); temp[2] = 0; hour = atoi(temp);
            strncpy(temp, input+10, 2); temp[2] = 0; minute = atoi(temp);
            strncpy(temp, input+12, 2); temp[2] = 0; second = atoi(temp);
            parsed_count = 6;
        }
    }

    // 验证解析结果
    if(parsed_count != 6) {
        return 0; // 解析失败
    }

    // 验证时间范围
    if(year < 2000 || year > 2099 || month < 1 || month > 12 ||
       date < 1 || date > 31 || hour < 0 || hour > 23 ||
       minute < 0 || minute > 59 || second < 0 || second > 59) {
        return 0; // 时间范围无效
    }

    // 转换为结构体格式
    time_data->year = year % 100;  // 只保留后两位
    time_data->month = month;
    time_data->date = date;
    time_data->hour = hour;
    time_data->minute = minute;
    time_data->second = second;

    return 1; // 解析成功
}

// RTC时间配置命令
void rtc_config_command(void)
{
    printf("Debug: Entering rtc_config_command function\r\n");

    // 默认先执行RTC重置，确保RTC处于干净状态
    printf("Performing automatic RTC reset before configuration...\r\n");

    // 确保可以访问备份域
    pmu_backup_write_enable();

    // 重置备份域（包括RTC）
    rcu_bkp_reset_enable();

    // 延时确保重置完成
    for(volatile uint32_t i = 0; i < 10000; i++);

    // 禁用重置
    rcu_bkp_reset_disable();

    printf("RTC reset completed. Ready for configuration.\r\n");

    // 重新初始化RTC时钟源
    local_rtc_init();

    printf("Input time (format: 2025-01-01 12:00:30):\r\n");

    char input_buffer[64] = {0};
    uint8_t input_index = 0;

    // 读取用户输入
    while(1) {
        if(usart_flag_get(USART0, USART_FLAG_RBNE) != RESET) {
            char ch = usart_data_receive(USART0);

            if(ch == '\r' || ch == '\n') {
                if(input_index > 0) {
                    input_buffer[input_index] = '\0';
                    break;
                }
            }
            else if(input_index < sizeof(input_buffer)-1) {
                input_buffer[input_index++] = ch;
                printf("%c", ch); // 回显字符
            }
        }
    }

    // 解析时间输入
    rtc_time_struct new_time;
    if(parse_time_input(input_buffer, &new_time)) {
        printf("\r\nParsed time: 20%02d-%02d-%02d %02d:%02d:%02d\r\n",
               new_time.year, new_time.month, new_time.date,
               new_time.hour, new_time.minute, new_time.second);

        // 简化的RTC时间设置（重置后直接配置）
        printf("Setting RTC time...\r\n");

        // 配置RTC参数结构
        rtc_parameter_struct rtc_initpara;

        rtc_initpara.factor_asyn = 0x7F;  // 异步预分频器
        rtc_initpara.factor_syn = 0xFF;   // 同步预分频器
        rtc_initpara.year = convert_to_bcd(new_time.year);
        rtc_initpara.month = convert_to_bcd(new_time.month);
        rtc_initpara.date = convert_to_bcd(new_time.date);
        rtc_initpara.hour = convert_to_bcd(new_time.hour);
        rtc_initpara.minute = convert_to_bcd(new_time.minute);
        rtc_initpara.second = convert_to_bcd(new_time.second);
        rtc_initpara.display_format = RTC_24HOUR;
        rtc_initpara.am_pm = RTC_AM;
        rtc_initpara.day_of_week = RTC_MONDAY; // 默认设为周一

        // 使用库函数初始化RTC（重置后应该能正常工作）
        if(rtc_init(&rtc_initpara) == SUCCESS) {
            printf("RTC Config success\r\n");
            printf("Time: 20%02d-%02d-%02d %02d:%02d:%02d\r\n",
                   new_time.year, new_time.month, new_time.date,
                   new_time.hour, new_time.minute, new_time.second);

            // 标记RTC已配置
            RTC_BKP0 = 0x32F0;
        } else {
            printf("RTC Config failed\r\n");
            printf("Please check RTC hardware and try again\r\n");
        }
    } else {
        printf("\r\nInvalid time format\r\n");
        printf("Please use format: 2025-01-01 12:00:30\r\n");
    }
}

// RTC当前时间显示命令
void rtc_now_command(void)
{
    get_current_time();
    printf("Current Time: 20%02d-%02d-%02d %02d:%02d:%02d\r\n",
           current_time.year, current_time.month, current_time.date,
           current_time.hour, current_time.minute, current_time.second);
}

// 本地RTC初始化函数
void local_rtc_init(void)
{
    // printf("Initializing RTC...\r\n");

    // 使能PMU时钟
    rcu_periph_clock_enable(RCU_PMU);
    // 使能对RTC寄存器的访问
    pmu_backup_write_enable();

    // 尝试使用外部低速晶振，如果失败则使用内部RC
    rcu_osci_on(RCU_LXTAL);

    // 等待外部晶振稳定，设置超时
    uint32_t timeout = 0x0000FFFF;
    while((RESET == rcu_flag_get(RCU_FLAG_LXTALSTB)) && (timeout > 0)) {
        timeout--;
    }

    if(timeout > 0) {
        // 外部晶振稳定，使用LXTAL
        rcu_rtc_clock_config(RCU_RTCSRC_LXTAL);
        // printf("RTC using external LXTAL\r\n");
    } else {
        // 外部晶振不稳定，使用内部IRC32K
        // printf("LXTAL timeout, using IRC32K\r\n");
        rcu_osci_on(RCU_IRC32K);
        rcu_osci_stab_wait(RCU_IRC32K);
        rcu_rtc_clock_config(RCU_RTCSRC_IRC32K);
    }

    // 使能RTC时钟
    rcu_periph_clock_enable(RCU_RTC);
    rtc_register_sync_wait();

    // printf("RTC clock configured and ready\r\n");
    // printf("Use 'RTC Config' to set time, 'RTC now' to check current time\r\n");
}

// RTC状态检查函数（用于调试）
void rtc_status_check(void)
{
    printf("=== RTC Status Check ===\r\n");

    // 检查RTC时钟源
    uint32_t rtc_source = GET_BITS(RCU_BDCTL, 8, 9);
    printf("RTC Clock Source: ");
    switch(rtc_source) {
        case 0: printf("No clock\r\n"); break;
        case 1: printf("LXTAL\r\n"); break;
        case 2: printf("IRC32K\r\n"); break;
        case 3: printf("HXTAL/32\r\n"); break;
        default: printf("Unknown\r\n"); break;
    }

    // 检查晶振状态
    if(rcu_flag_get(RCU_FLAG_LXTALSTB)) {
        printf("LXTAL: Stable\r\n");
    } else {
        printf("LXTAL: Not stable\r\n");
    }

    if(rcu_flag_get(RCU_FLAG_IRC32KSTB)) {
        printf("IRC32K: Stable\r\n");
    } else {
        printf("IRC32K: Not stable\r\n");
    }

    // 检查RTC寄存器状态
    printf("RTC_STAT: 0x%08X ", RTC_STAT);
    if(RTC_STAT & RTC_STAT_INITF) printf("(INITF) ");
    if(RTC_STAT & RTC_STAT_RSYNF) printf("(RSYNF) ");
    printf("\r\n");

    printf("RTC_CTL: 0x%08X\r\n", RTC_CTL);
    printf("RTC_PSC: 0x%08X\r\n", RTC_PSC);
    printf("RTC_TIME: 0x%08X\r\n", RTC_TIME);
    printf("RTC_DATE: 0x%08X\r\n", RTC_DATE);
    printf("RTC_BKP0: 0x%08X\r\n", RTC_BKP0);

    // 检查PMU和RCU状态
    printf("RCU_BDCTL: 0x%08X\r\n", RCU_BDCTL);
    printf("PMU_CTL: 0x%08X\r\n", PMU_CTL);

    printf("========================\r\n");
}

// RTC重置命令
void rtc_reset_command(void)
{
    printf("=== RTC Domain Reset ===\r\n");
    printf("Warning: This will reset all RTC settings!\r\n");
    printf("Performing RTC domain reset...\r\n");

    // 确保可以访问备份域
    pmu_backup_write_enable();

    // 重置备份域（包括RTC）
    rcu_bkp_reset_enable();

    // 延时确保重置完成
    for(volatile uint32_t i = 0; i < 10000; i++);

    // 禁用重置
    rcu_bkp_reset_disable();

    printf("RTC domain reset completed\r\n");
    printf("Please restart system and reconfigure RTC\r\n");
    printf("========================\r\n");
}

// 检查KEY1按键状态（无阻塞版本）
void check_key1_press(void)
{
    // 直接读取GPIO状态，不使用带延时的KEY_Stat函数
    uint8_t current_key_state = (gpio_input_bit_get(KEY_PORT, KEY1_PIN) == RESET) ? 1 : 0;

    // 检测按键从未按下到按下的状态变化（下降沿触发）
    if(current_key_state == 1 && key1_last_state == 0) {
        // 按键防抖处理
        key_debounce_counter++;
        if(key_debounce_counter >= 20) { // 防抖计数达到阈值（约20ms）
            if(!key1_pressed_flag) { // 避免重复触发
                toggle_sampling_by_key(); // 切换采样状态
                key1_pressed_flag = 1; // 设置按下标志
            }
            key_debounce_counter = 0; // 重置防抖计数器
        }
    } else if(current_key_state == 0) {
        // 按键释放
        key_debounce_counter = 0; // 重置防抖计数器
        key1_pressed_flag = 0; // 清除按下标志
    }

    key1_last_state = current_key_state; // 更新按键状态
}

// 通过按键切换采样状态
void toggle_sampling_by_key(void)
{
    if(sampling_active == 0) {
        // 当前停止状态，启动采样
        start_sampling();
    } else {
        // 当前运行状态，停止采样
        stop_sampling();
    }
}

// 检查KEY2/3/4按键状态
void check_key234_press(void)
{
    // 检查KEY2按键（5秒周期）
    uint8_t key2_state = (gpio_input_bit_get(KEY_PORT, KEY2_PIN) == RESET) ? 1 : 0;
    if(key2_state == 1 && key2_last_state == 0) {
        if(!key2_pressed_flag) {
            adjust_sample_cycle(5);
            key2_pressed_flag = 1;
        }
    } else if(key2_state == 0) {
        key2_pressed_flag = 0;
    }
    key2_last_state = key2_state;

    // 检查KEY3按键（10秒周期）
    uint8_t key3_state = (gpio_input_bit_get(KEY_PORT, KEY3_PIN) == RESET) ? 1 : 0;
    if(key3_state == 1 && key3_last_state == 0) {
        if(!key3_pressed_flag) {
            adjust_sample_cycle(10);
            key3_pressed_flag = 1;
        }
    } else if(key3_state == 0) {
        key3_pressed_flag = 0;
    }
    key3_last_state = key3_state;

    // 检查KEY4按键（15秒周期）
    uint8_t key4_state = (gpio_input_bit_get(KEY_PORT, KEY4_PIN) == RESET) ? 1 : 0;
    if(key4_state == 1 && key4_last_state == 0) {
        if(!key4_pressed_flag) {
            adjust_sample_cycle(15);
            key4_pressed_flag = 1;
        }
    } else if(key4_state == 0) {
        key4_pressed_flag = 0;
    }
    key4_last_state = key4_state;
}

// 调整采样周期
void adjust_sample_cycle(uint32_t new_cycle)
{
    if(new_cycle == 5 || new_cycle == 10 || new_cycle == 15) {
        sample_cycle = new_cycle;
        config_data.cycle = new_cycle;

        // 输出周期调整信息
        printf("=== >sample cycle adjust: %lus\r\n", sample_cycle);

        // 保存配置到Flash
        save_config_to_flash();

        // 如果正在采样，重新启动以应用新周期
        if(sampling_active) {
            last_sample_second = 255; // 重置采样时间点
        }

        // 更新OLED显示
        update_oled_display();
    }
}

// 获取Unix时间戳
uint32_t get_unix_timestamp(void)
{
    get_current_time();

    // 计算从1970年1月1日到当前时间的秒数
    // 简化计算：假设2000年1月1日的Unix时间戳为946684800
    uint32_t base_timestamp = 946684800; // 2000-01-01 00:00:00 UTC

    // 计算年份偏移（从2000年开始）
    uint32_t years = current_time.year; // 已经是从2000年开始的年份
    uint32_t days = years * 365 + (years / 4); // 简化闰年计算

    // 添加月份天数（简化计算）
    uint8_t days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    for(int i = 1; i < current_time.month; i++) {
        days += days_in_month[i-1];
    }

    // 添加当月天数
    days += (current_time.date - 1);

    // 转换为秒数
    uint32_t total_seconds = base_timestamp +
                           days * 24 * 3600 +
                           current_time.hour * 3600 +
                           current_time.minute * 60 +
                           current_time.second;

    return total_seconds;
}

// 电压值HEX编码
void encode_voltage_hex(float voltage, char* hex_str)
{
    // 分离整数部分和小数部分
    uint16_t integer_part = (uint16_t)voltage;
    float decimal_part = voltage - integer_part;

    // 小数部分转换为16位整数（乘以65536）
    uint16_t decimal_hex = (uint16_t)(decimal_part * 65536);

    // 格式化为8位HEX字符串（高位在前）
    sprintf(hex_str, "%04X%04X", integer_part, decimal_hex);
}

// 隐藏模式命令
void hide_command(void)
{
    if(!sampling_active) {
        printf("Error: Sampling not active. Please start sampling first.\r\n");
        return;
    }

    hide_mode = 1;
    printf("<=== hide\r\n");
    printf("Data format switched to HEX mode\r\n");
    write_log_data("Hide mode enabled");
}

// 取消隐藏模式命令
void unhide_command(void)
{
    if(!sampling_active) {
        printf("Error: Sampling not active. Please start sampling first.\r\n");
        return;
    }

    hide_mode = 0;
    printf("<=== unhide\r\n");
    printf("Data format restored to normal mode\r\n");
    write_log_data("Hide mode disabled");
}

// 命令匹配检查函数
uint8_t check_command_match(char* buffer, uint8_t length)
{
    // 检查是否匹配完整命令
    if(length == 1) {
        if(buffer[0] == 'h') {
            hide_command();
            return 1;
        }
        else if(buffer[0] == 'u') {
            unhide_command();
            return 1;
        }
        else if(buffer[0] == 's') {
            if(sampling_active) {
                stop_sampling();
            } else {
                start_sampling();
            }
            return 1;
        }
    }
    else if(length == 4 && strncmp(buffer, "hide", 4) == 0) {
        hide_command();
        return 1;
    }
    else if(length == 6 && strncmp(buffer, "unhide", 6) == 0) {
        unhide_command();
        return 1;
    }
    else if(length == 5 && strncmp(buffer, "start", 5) == 0) {
        start_sampling();
        return 1;
    }
    else if(length == 4 && strncmp(buffer, "stop", 4) == 0) {
        stop_sampling();
        return 1;
    }

    return 0; // 没有匹配
}

// 增加上电次数并保存到Flash
void increment_boot_count(void)
{
    config_data.boot_count++;
    save_config_to_flash();
    // printf("Boot count: %lu\r\n", config_data.boot_count);
}

// 获取14位数字时间格式
void get_datetime_string(char* datetime_str)
{
    get_current_time();
    sprintf(datetime_str, "20%02d%02d%02d%02d%02d%02d",
            current_time.year, current_time.month, current_time.date,
            current_time.hour, current_time.minute, current_time.second);
}

// 生成sample文件名
void generate_sample_filename(char* filename)
{
    char datetime_str[15];
    get_datetime_string(datetime_str);
    sprintf(filename, "0:/sample/sampleData%s.txt", datetime_str);
}

// 生成overLimit文件名
void generate_overlimit_filename(char* filename)
{
    char datetime_str[15];
    get_datetime_string(datetime_str);
    sprintf(filename, "0:/overLimit/overLimit%s.txt", datetime_str);
}

// 生成hideData文件名
void generate_hide_filename(char* filename)
{
    char datetime_str[15];
    get_datetime_string(datetime_str);
    sprintf(filename, "0:/hideData/hideData%s.txt", datetime_str);
}

// 生成log文件名
void generate_log_filename(char* filename)
{
    sprintf(filename, "0:/log/log%lu.txt", config_data.boot_count);
}

// 创建存储目录
void create_directories(void)
{
    FRESULT result;

    // 创建sample目录
    result = f_mkdir("0:/sample");
    if(result == FR_OK) {
        // printf("Created directory: sample\r\n");
    } else if(result == FR_EXIST) {
        // printf("Directory already exists: sample\r\n");
    } else {
        // printf("Failed to create directory: sample (error %d)\r\n", result);
    }

    // 创建overLimit目录
    result = f_mkdir("0:/overLimit");
    if(result == FR_OK) {
        // printf("Created directory: overLimit\r\n");
    } else if(result == FR_EXIST) {
        // printf("Directory already exists: overLimit\r\n");
    } else {
        // printf("Failed to create directory: overLimit (error %d)\r\n", result);
    }

    // 创建log目录
    result = f_mkdir("0:/log");
    if(result == FR_OK) {
        // printf("Created directory: log\r\n");
    } else if(result == FR_EXIST) {
        // printf("Directory already exists: log\r\n");
    } else {
        // printf("Failed to create directory: log (error %d)\r\n", result);
    }

    // 创建hideData目录
    result = f_mkdir("0:/hideData");
    if(result == FR_OK) {
        // printf("Created directory: hideData\r\n");
    } else if(result == FR_EXIST) {
        // printf("Directory already exists: hideData\r\n");
    } else {
        // printf("Failed to create directory: hideData (error %d)\r\n", result);
    }
}

// 初始化数据存储
void init_data_storage(void)
{
    // printf("Initializing data storage...\r\n");

    // 初始化文件管理器结构体
    memset(&file_manager, 0, sizeof(file_manager_t));
    file_manager.storage_enabled = 1; // 启用存储功能

    // 创建存储目录
    create_directories();

    // 创建日志文件
    char log_filename[64];
    generate_log_filename(log_filename);
    FRESULT result = f_open(&file_manager.log_file, log_filename, FA_CREATE_ALWAYS | FA_WRITE);
    if(result == FR_OK) {
        // printf("Created log file: %s\r\n", log_filename);
        // 记录系统启动日志
        write_log_data("System startup");
    } else {
        // printf("Failed to create log file (error %d)\r\n", result);
        file_manager.storage_enabled = 0;
    }

    // printf("Data storage initialization completed\r\n");
}

// 检查并创建新文件
void check_and_create_new_file(uint8_t file_type)
{
    char filename[64];
    FRESULT result;

    switch(file_type) {
        case 0: // sample文件
            if(file_manager.sample_count >= 10) {
                // 关闭当前文件
                f_close(&file_manager.sample_file);
                // 生成新文件名并创建
                generate_sample_filename(filename);
                result = f_open(&file_manager.sample_file, filename, FA_CREATE_ALWAYS | FA_WRITE);
                if(result == FR_OK) {
                    file_manager.sample_count = 0;
                    printf("Created new sample file: %s\r\n", filename);
                } else {
                    printf("Failed to create sample file (error %d)\r\n", result);
                    file_manager.storage_enabled = 0; // 禁用存储
                }
            }
            break;

        case 1: // hideData文件
            if(file_manager.hide_count >= 10) {
                // 关闭当前文件
                f_close(&file_manager.hide_file);
                // 生成新文件名并创建
                generate_hide_filename(filename);
                result = f_open(&file_manager.hide_file, filename, FA_CREATE_ALWAYS | FA_WRITE);
                if(result == FR_OK) {
                    file_manager.hide_count = 0;
                    printf("Created new hide file: %s\r\n", filename);
                } else {
                    printf("Failed to create hide file (error %d)\r\n", result);
                    file_manager.storage_enabled = 0; // 禁用存储
                }
            }
            break;

        case 2: // overLimit文件
            if(file_manager.overlimit_count >= 10) {
                // 关闭当前文件
                f_close(&file_manager.overlimit_file);
                // 生成新文件名并创建
                generate_overlimit_filename(filename);
                result = f_open(&file_manager.overlimit_file, filename, FA_CREATE_ALWAYS | FA_WRITE);
                if(result == FR_OK) {
                    file_manager.overlimit_count = 0;
                    printf("Created new overlimit file: %s\r\n", filename);
                } else {
                    printf("Failed to create overlimit file (error %d)\r\n", result);
                    file_manager.storage_enabled = 0; // 禁用存储
                }
            }
            break;
    }
}

// 写入采样数据
void write_sample_data(char* data_str, uint8_t is_hide_mode)
{
    if(!file_manager.storage_enabled) return; // 存储功能未启用

    FRESULT result;
    UINT bytes_written;
    char filename[64];

    if(is_hide_mode) {
        // 写入hideData文件夹
        if(file_manager.hide_count == 0) {
            // 第一次写入，创建新文件
            generate_hide_filename(filename);
            result = f_open(&file_manager.hide_file, filename, FA_CREATE_ALWAYS | FA_WRITE);
            if(result != FR_OK) {
                printf("Failed to create hide file (error %d)\r\n", result);
                file_manager.storage_enabled = 0;
                return;
            }
        }

        // 写入数据
        result = f_write(&file_manager.hide_file, data_str, strlen(data_str), &bytes_written);
        if(result == FR_OK) {
            result = f_write(&file_manager.hide_file, "\r\n", 2, &bytes_written);
            f_sync(&file_manager.hide_file); // 立即同步到存储设备
            file_manager.hide_count++;

            // 检查是否需要创建新文件
            check_and_create_new_file(1);
        } else {
            printf("Failed to write hide data (error %d)\r\n", result);
        }
    } else {
        // 写入sample文件夹
        if(file_manager.sample_count == 0) {
            // 第一次写入，创建新文件
            generate_sample_filename(filename);
            result = f_open(&file_manager.sample_file, filename, FA_CREATE_ALWAYS | FA_WRITE);
            if(result != FR_OK) {
                printf("Failed to create sample file (error %d)\r\n", result);
                file_manager.storage_enabled = 0;
                return;
            }
        }

        // 写入数据
        result = f_write(&file_manager.sample_file, data_str, strlen(data_str), &bytes_written);
        if(result == FR_OK) {
            result = f_write(&file_manager.sample_file, "\r\n", 2, &bytes_written);
            f_sync(&file_manager.sample_file); // 立即同步到存储设备
            file_manager.sample_count++;

            // 检查是否需要创建新文件
            check_and_create_new_file(0);
        } else {
            printf("Failed to write sample data (error %d)\r\n", result);
        }
    }
}

// 写入超限数据
void write_overlimit_data(char* data_str, float voltage, float limit)
{
    if(!file_manager.storage_enabled) return; // 存储功能未启用

    FRESULT result;
    UINT bytes_written;
    char filename[64];
    char overlimit_str[128];

    // 格式化超限数据字符串
    sprintf(overlimit_str, "%s ch0=%.1fV OverLimit (%.2f)!", data_str, voltage, limit);

    if(file_manager.overlimit_count == 0) {
        // 第一次写入，创建新文件
        generate_overlimit_filename(filename);
        result = f_open(&file_manager.overlimit_file, filename, FA_CREATE_ALWAYS | FA_WRITE);
        if(result != FR_OK) {
            printf("Failed to create overlimit file (error %d)\r\n", result);
            file_manager.storage_enabled = 0;
            return;
        }
    }

    // 写入数据
    result = f_write(&file_manager.overlimit_file, overlimit_str, strlen(overlimit_str), &bytes_written);
    if(result == FR_OK) {
        result = f_write(&file_manager.overlimit_file, "\r\n", 2, &bytes_written);
        f_sync(&file_manager.overlimit_file); // 立即同步到存储设备
        file_manager.overlimit_count++;

        // 检查是否需要创建新文件
        check_and_create_new_file(2);
    } else {
        printf("Failed to write overlimit data (error %d)\r\n", result);
    }
}

// 写入日志数据
void write_log_data(char* operation)
{
    if(!file_manager.storage_enabled) return; // 存储功能未启用

    FRESULT result;
    UINT bytes_written;
    char log_str[128];
    char time_str[32];

    // 获取当前时间字符串
    get_current_time_string(time_str);

    // 格式化日志字符串
    sprintf(log_str, "%s - %s", time_str, operation);

    // 写入日志数据
    result = f_write(&file_manager.log_file, log_str, strlen(log_str), &bytes_written);
    if(result == FR_OK) {
        result = f_write(&file_manager.log_file, "\r\n", 2, &bytes_written);
        f_sync(&file_manager.log_file); // 立即同步到存储设备
    } else {
        printf("Failed to write log data (error %d)\r\n", result);
    }
}

// 保存设备ID到Flash
void save_device_id_to_flash(void)
{
    spi_flash_sector_erase(DEVICE_ID_FLASH_ADDRESS);
    spi_flash_buffer_write((uint8_t*)device_id_string, DEVICE_ID_FLASH_ADDRESS, strlen(device_id_string));
}

// 从Flash读取设备ID
void read_device_id_from_flash(void)
{
    char read_device_id[64] = {0}; // 缓冲区用于读取设备ID

    // 从Flash读取设备ID
    spi_flash_buffer_read((uint8_t*)read_device_id, DEVICE_ID_FLASH_ADDRESS, strlen(device_id_string));

    // 检查读取的数据是否有效（以"Device_ID:"开头）
    if(strncmp(read_device_id, "Device_ID:", 10) == 0) {
        // 数据有效，打印读取的设备ID
        printf("%s\r\n", read_device_id);
    } else {
        // 数据无效，保存默认设备ID到Flash并打印
        save_device_id_to_flash();
        printf("%s\r\n", device_id_string);
    }
}


