# HEX格式功能使用说明

## 🎯 功能概述

在采样运行状态下，可以通过`hide`和`unhide`命令在正常时间戳格式和HEX格式之间切换数据显示。

## 📋 使用流程

### 1. 启动采样（必须先执行）
```bash
start
```
**输出：**
```
Periodic Sampling
sample cycle: 5s
2025-01-01 12:00:05 ch0=10.5V
2025-01-01 12:00:10 ch0=8.2V
```

### 2. 切换到HEX模式
```bash
hide
```
**输出：**
```
<=== hide
Data format switched to HEX mode
=== >6774C4F5000C8000
=== >6774C4FA000C8000
```

### 3. 恢复正常模式
```bash
unhide
```
**输出：**
```
<=== unhide
Data format restored to normal mode
2025-01-01 12:00:20 ch0=10.5V
```

### 4. 停止采样（自动恢复正常模式）
```bash
stop
```
**输出：**
```
Periodic Sampling STOP
Data format restored to normal mode  # 仅在HEX模式时显示
```

## ⚠️ 重要限制

### hide/unhide命令只能在采样运行时使用！

**错误示例：**
```bash
# 在未启动采样时使用
hide
# 输出：Error: Sampling not active. Please start sampling first.

unhide
# 输出：Error: Sampling not active. Please start sampling first.
```

## 📊 数据格式说明

### HEX编码规则
- **时间戳**：4字节Unix时间戳（如：6774C4F5）
- **电压值**：4字节（整数2字节+小数2字节，如：000C8000）
- **超限标记**：在HEX数据后添加`*`

### 示例对比
| 模式 | 正常情况 | 超限情况 |
|------|----------|----------|
| **正常** | `2025-01-01 12:30:45 ch0=12.5V` | `=== >2025-01-01 12:30:45 ch0=15.5V OverLimit (10.00)!` |
| **HEX** | `=== >6774C4F5000C8000` | `=== >6774C4F5000F8000*` |

## 🔧 技术细节

### 时间戳转换
- 2025-01-01 12:30:45 → Unix时间戳1735705845 → HEX: 6774C4F5

### 电压值编码
- 12.5V → 整数部分12(000C) + 小数部分0.5×65536=32768(8000) → 000C8000

## 💡 使用技巧

1. **必须先启动采样**：hide/unhide命令依赖于采样状态
2. **自动恢复**：停止采样时自动恢复正常模式
3. **实时切换**：可以在采样过程中随时切换显示格式
4. **超限检测**：HEX模式下超限用`*`标记，正常模式用文字说明

## 🧪 快速测试

```bash
# 完整测试流程
start          # 启动采样，观察正常格式
hide           # 切换HEX格式，观察数据变化
unhide         # 恢复正常格式
stop           # 停止采样

# 错误测试
hide           # 应该报错：需要先启动采样
```

## 📝 注意事项

- hide/unhide状态不会保存到Flash，重启后恢复默认（正常模式）
- 超限检测在两种模式下都正常工作
- OLED显示不受hide/unhide影响，始终显示正常格式
- LED2超限指示在两种模式下都正常工作
