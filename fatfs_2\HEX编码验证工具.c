/*
 * HEX编码验证工具
 * 用于验证时间戳和电压值的HEX编码是否正确
 */

#include <stdio.h>
#include <stdint.h>
#include <time.h>

// 模拟RTC时间结构
typedef struct {
    uint8_t year;   // 从2000年开始
    uint8_t month;
    uint8_t date;
    uint8_t hour;
    uint8_t minute;
    uint8_t second;
} rtc_time_struct;

// 获取Unix时间戳（模拟嵌入式版本）
uint32_t get_unix_timestamp_sim(rtc_time_struct* time_data)
{
    // 计算从1970年1月1日到当前时间的秒数
    // 简化计算：假设2000年1月1日的Unix时间戳为946684800
    uint32_t base_timestamp = 946684800; // 2000-01-01 00:00:00 UTC
    
    // 计算年份偏移（从2000年开始）
    uint32_t years = time_data->year; // 已经是从2000年开始的年份
    uint32_t days = years * 365 + (years / 4); // 简化闰年计算
    
    // 添加月份天数（简化计算）
    uint8_t days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    for(int i = 1; i < time_data->month; i++) {
        days += days_in_month[i-1];
    }
    
    // 添加当月天数
    days += (time_data->date - 1);
    
    // 转换为秒数
    uint32_t total_seconds = base_timestamp + 
                           days * 24 * 3600 + 
                           time_data->hour * 3600 + 
                           time_data->minute * 60 + 
                           time_data->second;
    
    return total_seconds;
}

// 电压值HEX编码（模拟嵌入式版本）
void encode_voltage_hex_sim(float voltage, char* hex_str)
{
    // 分离整数部分和小数部分
    uint16_t integer_part = (uint16_t)voltage;
    float decimal_part = voltage - integer_part;
    
    // 小数部分转换为16位整数（乘以65536）
    uint16_t decimal_hex = (uint16_t)(decimal_part * 65536);
    
    // 格式化为8位HEX字符串（高位在前）
    sprintf(hex_str, "%04X%04X", integer_part, decimal_hex);
}

// 解码HEX时间戳
void decode_timestamp(uint32_t timestamp)
{
    time_t t = (time_t)timestamp;
    struct tm* tm_info = gmtime(&t);
    
    printf("解码时间戳 0x%08X (%u):\n", timestamp, timestamp);
    printf("  时间: %04d-%02d-%02d %02d:%02d:%02d UTC\n",
           tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday,
           tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec);
}

// 解码HEX电压值
void decode_voltage(const char* hex_str)
{
    uint32_t hex_value;
    sscanf(hex_str, "%X", &hex_value);
    
    uint16_t integer_part = (hex_value >> 16) & 0xFFFF;
    uint16_t decimal_part = hex_value & 0xFFFF;
    
    float voltage = integer_part + (float)decimal_part / 65536.0;
    
    printf("解码电压值 %s:\n", hex_str);
    printf("  整数部分: 0x%04X (%d)\n", integer_part, integer_part);
    printf("  小数部分: 0x%04X (%d/65536 = %.6f)\n", decimal_part, decimal_part, (float)decimal_part/65536.0);
    printf("  电压值: %.6f V\n", voltage);
}

int main()
{
    printf("=== HEX编码验证工具 ===\n\n");
    
    // 测试用例1：2025-01-01 12:30:45, 12.5V
    printf("测试用例1: 2025-01-01 12:30:45, 12.5V\n");
    printf("----------------------------------------\n");
    
    rtc_time_struct test_time1 = {25, 1, 1, 12, 30, 45}; // 2025年
    float test_voltage1 = 12.5;
    
    uint32_t timestamp1 = get_unix_timestamp_sim(&test_time1);
    char voltage_hex1[9];
    encode_voltage_hex_sim(test_voltage1, voltage_hex1);
    
    printf("编码结果:\n");
    printf("  时间戳: 0x%08X\n", timestamp1);
    printf("  电压值: %s\n", voltage_hex1);
    printf("  完整HEX: %08X%s\n", timestamp1, voltage_hex1);
    
    decode_timestamp(timestamp1);
    decode_voltage(voltage_hex1);
    
    printf("\n");
    
    // 测试用例2：2025-01-01 12:30:50, 15.3V
    printf("测试用例2: 2025-01-01 12:30:50, 15.3V\n");
    printf("----------------------------------------\n");
    
    rtc_time_struct test_time2 = {25, 1, 1, 12, 30, 50}; // 2025年
    float test_voltage2 = 15.3;
    
    uint32_t timestamp2 = get_unix_timestamp_sim(&test_time2);
    char voltage_hex2[9];
    encode_voltage_hex_sim(test_voltage2, voltage_hex2);
    
    printf("编码结果:\n");
    printf("  时间戳: 0x%08X\n", timestamp2);
    printf("  电压值: %s\n", voltage_hex2);
    printf("  完整HEX: %08X%s\n", timestamp2, voltage_hex2);
    
    decode_timestamp(timestamp2);
    decode_voltage(voltage_hex2);
    
    printf("\n");
    
    // 验证示例数据
    printf("验证示例数据:\n");
    printf("----------------------------------------\n");
    printf("预期: 2025-01-01 12:30:45 → 6774C4F5\n");
    printf("实际: 2025-01-01 12:30:45 → %08X\n", timestamp1);
    printf("匹配: %s\n", (timestamp1 == 0x6774C4F5) ? "是" : "否");
    
    printf("\n预期: 12.5V → 000C8000\n");
    printf("实际: 12.5V → %s\n", voltage_hex1);
    printf("匹配: %s\n", (strcmp(voltage_hex1, "000C8000") == 0) ? "是" : "否");
    
    return 0;
}
