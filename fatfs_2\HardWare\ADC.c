#include "ADC.h"
void ADC_port_init(void)
{
	rcu_periph_clock_enable(RCU_GPIOC);   // GPIOCʱ��ʹ��
	rcu_periph_clock_enable(RCU_ADC0);    // ʹ��ADCʱ��
	
	gpio_mode_set(GPIOC, GPIO_MODE_ANALOG, GPIO_PUPD_NONE, GPIO_PIN_0);   // ����PC0Ϊģ������
	
	adc_clock_config(ADC_ADCCK_PCLK2_DIV8);   // adcʱ������
	
	ADC_Init();  // ADC����
	
	adc_software_trigger_enable(ADC0, ADC_ROUTINE_CHANNEL); //  ���������������
	

}
void ADC_Init(void)
{
    adc_deinit();    // ��λADC
	
    adc_special_function_config(ADC0, ADC_CONTINUOUS_MODE, ENABLE);    	// ʹ������ת��ģʽ
    adc_data_alignment_config(ADC0, ADC_DATAALIGN_RIGHT);   			// �����Ҷ��� 
    adc_channel_length_config(ADC0, ADC_ROUTINE_CHANNEL, 1);  			// ͨ�����ã�������

    adc_routine_channel_config(ADC0, 0, ADC_CHANNEL_10, ADC_SAMPLETIME_56);   // �Թ������������

    adc_external_trigger_source_config(ADC0, ADC_ROUTINE_CHANNEL, ADC_EXTTRIG_INSERTED_T0_CH3);   // ADC �������ã����ö�ʱ������
    adc_external_trigger_config(ADC0, ADC_ROUTINE_CHANNEL, ENABLE);   							  // ���ô���
	
    adc_enable(ADC0);   		// ʹ��ADC�ӿ�
	
    delay_1ms(1);  				// �ȴ�1ms

    adc_calibration_enable(ADC0);    // ADCУ׼�͸�λADCУ׼
}

// 读取ADC原始值
uint16_t ADC_Read_Value(void)
{
    // 启动ADC转换
    adc_software_trigger_enable(ADC0, ADC_ROUTINE_CHANNEL);

    // 等待转换完成
    while(!adc_flag_get(ADC0, ADC_FLAG_EOC));

    // 读取转换结果
    uint16_t adc_value = adc_routine_data_read(ADC0);

    // 清除转换完成标志
    adc_flag_clear(ADC0, ADC_FLAG_EOC);

    return adc_value;
}

// 读取ADC电压值
float ADC_Read_Voltage(void)
{
    uint16_t adc_value = ADC_Read_Value();

    // 转换为电压值
    // ADC参考电压3.3V，12位ADC，最大值4095
    float voltage = (float)adc_value * 3.3f / 4095.0f;

    // 根据实际的分压电路调整电压值
    // 假设有分压电路，需要根据实际硬件调整
    voltage = voltage * 4.0f; // 假设4倍分压，实际需要根据硬件调整

    return voltage;
}



