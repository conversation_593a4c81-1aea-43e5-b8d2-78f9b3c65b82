/************************************************************
 * ��Ȩ��2025CIMC Copyright�� 
 * �ļ���adc.h
 * ����: Lingyu Meng
 * ƽ̨: 2025CIMC IHD-V04
 * �汾: Lingyu Meng     2025/03/07     V0.01    original
************************************************************/

#ifndef __ADC_H
#define __ADC_H

/************************* ͷ�ļ� *************************/

#include "HeaderFiles.h"

/************************* �궨�� *************************/


/************************ �������� ************************/


/************************ �������� ************************/

void ADC_port_init(void); //adc�˿ڳ�ʼ��
void ADC_Init(void);     //  ADC ��ʼ��
uint16_t ADC_Read_Value(void); // 读取ADC原始值
float ADC_Read_Voltage(void);  // 读取ADC电压值
				    
#endif


/****************************End*****************************/

