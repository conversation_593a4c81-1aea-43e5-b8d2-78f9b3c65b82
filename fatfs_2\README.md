# GD32F4xx 嵌入式系统项目

## 项目概述
基于GD32F4xx微控制器的嵌入式系统，集成了TF卡、SPI Flash、RTC、ADC、OLED显示等功能模块，支持数据采样、配置管理和按键控制。

## 主要功能

### 1. 系统自检
- **命令**: `test` 或 `?` 或 `t`
- **功能**: 检测Flash和TF卡状态，显示设备信息

### 2. 配置管理
- **读取配置**: `conf` - 从TF卡读取config.ini配置文件
- **设置变比**: `ratio` - 设置变比值(0~100)
- **设置阈值**: `limit` - 设置阈值(0~500)
- **保存配置**: `config save` - 保存参数到Flash
- **读取配置**: `config read` - 从Flash读取参数

### 3. 采样控制
#### 串口命令控制
- **启动采样**: `start` - 开始周期性采样
- **停止采样**: `stop` - 停止采样

#### 按键控制（新增功能）
- **KEY1按键**: 按下KEY1可切换采样状态
  - 当前停止状态 → 按下KEY1 → 启动采样
  - 当前采样状态 → 按下KEY1 → 停止采样
  - 输出格式与串口命令完全一致

#### 周期调整（新增功能）
- **KEY2按键**: 设置采样周期为5秒
- **KEY3按键**: 设置采样周期为10秒
- **KEY4按键**: 设置采样周期为15秒
- **输出格式**: `=== >sample cycle adjust: 10s`
- **持久化存储**: 周期设置自动保存到Flash，断电重启后生效
- **实时生效**: 采样过程中按键调整周期立即生效

### 4. 采样功能特性
- **采样周期**: 5秒间隔
- **LED指示**: LED1在采样期间1秒周期闪烁
- **OLED显示**: 
  - 采样时显示时间(hh:mm:ss)和电压(xx.xx V)
  - 空闲时显示"system idle"
- **数据输出**: 串口输出格式 `2025-01-01 00:30:05 ch0=10.5V`
- **电压采集**: 使用ADC硬件真实采集电压值

### 5. RTC时间管理
- **设置时间**: `RTC Config` - 配置RTC时间
- **查看时间**: `RTC now` - 显示当前时间
- **状态检查**: `RTC status` - 显示RTC调试信息
- **重置RTC**: `RTC reset` - 重置RTC域

### 6. 数据存储功能（新增）
系统支持将采样数据、超限事件和操作日志自动存储到TF卡，提供完整的数据记录和追踪能力。

#### 存储类型
1. **采样数据存储**
   - **目录**: `0:/sample/`
   - **文件名格式**: `sampleData{datetime}.txt`
   - **存储条件**: 正常采样模式(hide_mode=0)
   - **文件容量**: 每个文件最多10条数据
   - **数据格式**: `2025-01-01 00:30:05 ch0=10.5V`

2. **加密数据存储**
   - **目录**: `0:/hideData/`
   - **文件名格式**: `hideData{datetime}.txt`
   - **存储条件**: HEX模式(hide_mode=1)
   - **文件容量**: 每个文件最多10条数据
   - **数据格式**: `=== >65E5A5C500002710` (HEX格式)

3. **超限数据存储**
   - **目录**: `0:/overLimit/`
   - **文件名格式**: `overLimit{datetime}.txt`
   - **存储条件**: 电压超过设定阈值时
   - **文件容量**: 每个文件最多10条数据
   - **数据格式**: `2025-01-01 00:30:05 ch0=10.5V OverLimit (10.00)!`
   - **特性**: 无论hide_mode状态如何都会记录

4. **操作日志存储**
   - **目录**: `0:/log/`
   - **文件名格式**: `log{id}.txt` (id为上电次数)
   - **存储条件**: 系统运行期间的关键操作
   - **文件特性**: 每次上电创建新文件，运行期间持续记录
   - **日志内容**: 系统启动、采样控制、模式切换、配置保存等

#### 文件命名规则
- **时间格式**: 14位连续数字，如`20250101003010`表示2025-01-01 00:30:10
- **上电计数**: 存储在Flash中，每次重启自动递增
- **示例文件名**:
  - `sampleData20250101003010.txt`
  - `overLimit20250101003015.txt`
  - `hideData20250101003020.txt`
  - `log0.txt` (第一次上电)

#### 存储特性
- **自动管理**: 系统启动时自动创建目录结构
- **容错处理**: TF卡异常时跳过文件操作，不影响采样功能
- **即时同步**: 数据写入后立即同步到存储设备
- **文件切换**: 达到10条数据时自动创建新文件

### 7. 数据格式转换
- **隐藏模式**: `hide` - 将数据转换为HEX格式显示
- **恢复模式**: `unhide` - 恢复正常格式显示
- **使用条件**: 仅在采样期间可用

### 8. 文件操作
- **文件测试**: `tragll` - 执行FATFS文件读写测试

## 硬件配置

### 按键配置
- **KEY1**: GPIOE.PIN2 - 采样启停控制
- **KEY2**: GPIOE.PIN3 - 设置采样周期为5秒
- **KEY3**: GPIOE.PIN4 - 设置采样周期为10秒
- **KEY4**: GPIOE.PIN5 - 设置采样周期为15秒

### LED配置
- **LED1**: 采样状态指示，采样时1秒周期闪烁
- **LED2**: 超限状态指示，电压超过阈值时点亮

### 其他硬件
- **ADC**: 电压采集
- **OLED**: 状态显示
- **SPI Flash**: 配置数据存储
- **TF卡**: 配置文件读取
- **RTC**: 时间戳生成

## 使用说明

### 基本操作流程
1. 系统启动后执行自检: `test`
2. 配置RTC时间: `RTC Config`
3. 设置采样参数: `ratio` 和 `limit`
4. 调整采样周期: 按下KEY2(5s)/KEY3(10s)/KEY4(15s)
5. 启动采样: `start` 或按下KEY1
6. 停止采样: `stop` 或再次按下KEY1
7. 数据格式切换: `hide`/`unhide` (仅采样期间可用)

### 数据存储操作
- **自动存储**: 系统启动时自动初始化存储功能
- **目录结构**: 自动创建sample、overLimit、log、hideData目录
- **文件管理**: 每个文件最多10条数据，自动创建新文件
- **错误处理**: TF卡异常时存储功能自动禁用，不影响采样
- **日志追踪**: 所有关键操作自动记录到日志文件

### 按键操作说明
- **按键防抖**: 内置防抖处理，避免误触发
- **状态切换**: KEY1按键按下时检测状态变化，实现启停切换
- **周期调整**: KEY2/3/4按键实现5s/10s/15s周期切换
- **输出一致性**: 按键控制与串口命令输出格式完全一致
- **持久化存储**: 周期调整自动保存到Flash，断电重启后保持设置

### 配置文件格式
TF卡根目录下的config.ini文件格式：
```
Ratio=1.5
Limit=100.0
```

## 技术特性
- **实时采样**: 基于RTC真实时间的可调间隔采样(5s/10s/15s)
- **数据持久化**: 配置参数(变比/阈值/周期)自动保存到SPI Flash
- **多种控制方式**: 支持串口命令和按键控制
- **状态指示**: LED和OLED双重状态显示
- **防抖处理**: 按键防抖确保操作可靠性
- **动态调整**: 采样过程中可实时调整周期，立即生效
- **数据存储**: 自动存储采样数据、超限事件和操作日志到TF卡
- **文件管理**: 智能文件切换，每个文件最多10条数据
- **超限检测**: 实时超限检测，LED2指示和独立文件记录
- **格式转换**: 支持正常和HEX两种数据格式
- **容错设计**: TF卡异常时存储功能自动禁用，不影响采样
- **上电计数**: Flash存储上电次数，确保日志文件唯一性

## 开发环境
- **MCU**: GD32F4xx系列
- **开发工具**: Keil MDK
- **库文件**: GD32F4xx标准外设库

## 版本信息
- **当前版本**: V0.2
- **最后更新**: 2025-06-15
- **新增功能**:
  - KEY1按键控制采样启停
  - KEY2/3/4按键控制周期调整(5s/10s/15s)
  - 采样周期持久化存储
  - 动态周期调整功能
