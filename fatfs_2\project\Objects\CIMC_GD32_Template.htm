<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\CIMC_GD32_Template.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\CIMC_GD32_Template.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060750: Last Updated: Sun May 18 17:32:02 2025
<BR><P>
<H3>Maximum Stack Usage =        416 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; UsrFunction &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1c]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1c]">ADC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1c]">ADC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4]">BusFault_Handler</a> from gd32f4xx_it.o(i.BusFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[20]">CAN0_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1e]">CAN0_RX0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1f]">CAN0_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1d]">CAN0_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4c]">CAN1_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4a]">CAN1_RX0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4b]">CAN1_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[49]">CAN1_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[58]">DCI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[15]">DMA0_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[16]">DMA0_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[17]">DMA0_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[18]">DMA0_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[19]">DMA0_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1a]">DMA0_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1b]">DMA0_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[39]">DMA0_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[42]">DMA1_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[43]">DMA1_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[44]">DMA1_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[45]">DMA1_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[46]">DMA1_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4e]">DMA1_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4f]">DMA1_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[50]">DMA1_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[7]">DebugMon_Handler</a> from gd32f4xx_it.o(i.DebugMon_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[47]">ENET_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[48]">ENET_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3a]">EXMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[10]">EXTI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[32]">EXTI10_15_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[11]">EXTI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[12]">EXTI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[13]">EXTI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[14]">EXTI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[21]">EXTI5_9_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[e]">FMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5a]">FPU_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from gd32f4xx_it.o(i.HardFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2a]">I2C0_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[29]">I2C0_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2c]">I2C1_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2b]">I2C1_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[53]">I2C2_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[52]">I2C2_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[62]">IPA_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[b]">LVD_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3]">MemManage_Handler</a> from gd32f4xx_it.o(i.MemManage_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from gd32f4xx_it.o(i.NMI_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[8]">PendSV_Handler</a> from gd32f4xx_it.o(i.PendSV_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[f]">RCU_CTC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[33]">RTC_Alarm_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[d]">RTC_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3b]">SDIO_IRQHandler</a> from gd32f4xx_it.o(i.SDIO_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2d]">SPI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2e]">SPI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3d]">SPI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5d]">SPI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5e]">SPI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5f]">SPI5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6]">SVC_Handler</a> from gd32f4xx_it.o(i.SVC_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[9]">SysTick_Handler</a> from gd32f4xx_it.o(i.SysTick_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[64]">SystemInit</a> from system_gd32f4xx.o(i.SystemInit) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[c]">TAMPER_STAMP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[22]">TIMER0_BRK_TIMER8_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[25]">TIMER0_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[24]">TIMER0_TRG_CMT_TIMER10_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[23]">TIMER0_UP_TIMER9_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[26]">TIMER1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[27]">TIMER2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[28]">TIMER3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3c]">TIMER4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[40]">TIMER5_DAC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[41]">TIMER6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[35]">TIMER7_BRK_TIMER11_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[38]">TIMER7_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[37]">TIMER7_TRG_CMT_TIMER13_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[36]">TIMER7_UP_TIMER12_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[61]">TLI_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[60]">TLI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[59]">TRNG_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3e]">UART3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3f]">UART4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5b]">UART6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5c]">UART7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2f]">USART0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[30]">USART1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[31]">USART2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[51]">USART5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4d]">USBFS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[34]">USBFS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[55]">USBHS_EP1_In_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[54]">USBHS_EP1_Out_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[57]">USBHS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[56]">USBHS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5]">UsageFault_Handler</a> from gd32f4xx_it.o(i.UsageFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[a]">WWDGT_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[65]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[66]">fputc</a> from usart.o(i.fputc) referenced from printf3.o(i.__0printf$3)
 <LI><a href="#[63]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[65]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[ed]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[67]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[69]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[ee]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[ef]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[f0]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[f1]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[f2]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>CAN0_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>CAN0_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>CAN0_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>CAN0_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>CAN1_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>DCI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>DMA0_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>DMA0_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA0_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA0_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA0_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA0_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA0_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>DMA0_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>DMA1_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>ENET_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>ENET_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>EXMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>EXTI10_15_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>EXTI5_9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>I2C0_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>I2C0_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>IPA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>LVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>RCU_CTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>TAMPER_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>TIMER0_BRK_TIMER8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIMER0_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIMER0_TRG_CMT_TIMER10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>TIMER0_UP_TIMER9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIMER1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIMER2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIMER3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>TIMER4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>TIMER5_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>TIMER6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>TIMER7_BRK_TIMER11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIMER7_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIMER7_TRG_CMT_TIMER13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TIMER7_UP_TIMER12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>TLI_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>TLI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>TRNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>UART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>UART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>USART0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>USART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>USBFS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>USBFS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>USBHS_EP1_In_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>USBHS_EP1_Out_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>USBHS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>USBHS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>WWDGT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[f3]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[84]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[68]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[f4]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[4]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6a]"></a>LED_Init</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, led.o(i.LED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = LED_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[3]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>SDIO_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.SDIO_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SDIO_IRQHandler &rArr; sd_interrupts_process &rArr; sd_transfer_stop &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_interrupts_process
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>SysTick_Handler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTick_Handler
</UL>
<BR>[Calls]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_decrement
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>SystemInit</STRONG> (Thumb, 194 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SystemInit &rArr; system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[72]"></a>System_Init</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, function.o(i.System_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = System_Init &rArr; systick_config &rArr; NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_config
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[74]"></a>UsrFunction</STRONG> (Thumb, 290 bytes, Stack size 0 bytes, function.o(i.UsrFunction))
<BR><BR>[Stack]<UL><LI>Max Depth = 408<LI>Call Chain = UsrFunction &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_file
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_config
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memory_compare
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[82]"></a>__0printf$3</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, printf3.o(i.__0printf$3), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[f5]"></a>__1printf$3</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printf3.o(i.__0printf$3), UNUSED)

<P><STRONG><a name="[78]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printf3.o(i.__0printf$3))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_file
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[f6]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[f7]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[f8]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[97]"></a>clust2sect</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, ff.o(i.clust2sect))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>

<P><STRONG><a name="[81]"></a>delay_1ms</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, systick.o(i.delay_1ms))
<BR><BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[70]"></a>delay_decrement</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, systick.o(i.delay_decrement))
<BR><BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[77]"></a>disk_initialize</STRONG> (Thumb, 134 bytes, Stack size 88 bytes, diskio.o(i.disk_initialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = disk_initialize &rArr; sd_bus_mode_config &rArr; sd_bus_width_config &rArr; sd_scr_get &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_mode_config
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_select_deselect
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_information_get
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_mode_config
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
</UL>

<P><STRONG><a name="[e8]"></a>disk_ioctl</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, diskio.o(i.disk_ioctl))
<BR><BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
</UL>

<P><STRONG><a name="[86]"></a>disk_read</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, diskio.o(i.disk_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = disk_read &rArr; sd_multiblocks_read &rArr; dma_receive_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
</UL>

<P><STRONG><a name="[88]"></a>disk_status</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, diskio.o(i.disk_status))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
</UL>

<P><STRONG><a name="[a2]"></a>disk_write</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, diskio.o(i.disk_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>

<P><STRONG><a name="[a7]"></a>dma_channel_disable</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_channel_disable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_channel_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>

<P><STRONG><a name="[ac]"></a>dma_channel_enable</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_channel_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_channel_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>

<P><STRONG><a name="[ab]"></a>dma_channel_subperipheral_select</STRONG> (Thumb, 38 bytes, Stack size 12 bytes, gd32f4xx_dma.o(i.dma_channel_subperipheral_select))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = dma_channel_subperipheral_select
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>

<P><STRONG><a name="[a8]"></a>dma_deinit</STRONG> (Thumb, 166 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_deinit
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>

<P><STRONG><a name="[a6]"></a>dma_flag_clear</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_flag_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>

<P><STRONG><a name="[d4]"></a>dma_flag_get</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[aa]"></a>dma_flow_controller_config</STRONG> (Thumb, 64 bytes, Stack size 12 bytes, gd32f4xx_dma.o(i.dma_flow_controller_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = dma_flow_controller_config
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>

<P><STRONG><a name="[a9]"></a>dma_multi_data_mode_init</STRONG> (Thumb, 352 bytes, Stack size 16 bytes, gd32f4xx_dma.o(i.dma_multi_data_mode_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = dma_multi_data_mode_init
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>

<P><STRONG><a name="[7d]"></a>f_close</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, ff.o(i.f_close))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = f_close &rArr; f_sync &rArr; sync &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[79]"></a>f_mount</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, ff.o(i.f_mount))
<BR><BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[7a]"></a>f_open</STRONG> (Thumb, 364 bytes, Stack size 80 bytes, ff.o(i.f_open))
<BR><BR>[Stack]<UL><LI>Max Depth = 408<LI>Call Chain = f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[7e]"></a>f_read</STRONG> (Thumb, 462 bytes, Stack size 64 bytes, ff.o(i.f_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = f_read &rArr; get_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[ae]"></a>f_sync</STRONG> (Thumb, 184 bytes, Stack size 24 bytes, ff.o(i.f_sync))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = f_sync &rArr; sync &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
</UL>

<P><STRONG><a name="[7c]"></a>f_write</STRONG> (Thumb, 526 bytes, Stack size 64 bytes, ff.o(i.f_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[66]"></a>fputc</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, usart.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = fputc &rArr; usart_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printf3.o(i.__0printf$3)
</UL>
<P><STRONG><a name="[76]"></a>gd_eval_com_init</STRONG> (Thumb, 138 bytes, Stack size 8 bytes, usart.o(i.gd_eval_com_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = gd_eval_com_init &rArr; usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_transmit_config
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_receive_config
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_enable
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[8d]"></a>get_fat</STRONG> (Thumb, 228 bytes, Stack size 24 bytes, ff.o(i.get_fat))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = get_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>

<P><STRONG><a name="[b0]"></a>get_fattime</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, diskio.o(i.get_fattime))
<BR><BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
</UL>

<P><STRONG><a name="[b6]"></a>gpio_af_set</STRONG> (Thumb, 94 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_af_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_config
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[6e]"></a>gpio_bit_reset</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_reset))
<BR><BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[80]"></a>gpio_bit_set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_set))
<BR><BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[6c]"></a>gpio_mode_set</STRONG> (Thumb, 78 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_mode_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_config
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
</UL>

<P><STRONG><a name="[6d]"></a>gpio_output_options_set</STRONG> (Thumb, 66 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_output_options_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_output_options_set
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_config
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
</UL>

<P><STRONG><a name="[63]"></a>main</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = main &rArr; UsrFunction &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[7f]"></a>memory_compare</STRONG> (Thumb, 36 bytes, Stack size 12 bytes, function.o(i.memory_compare))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = memory_compare
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[75]"></a>nvic_config</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, function.o(i.nvic_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = nvic_config &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[be]"></a>nvic_irq_enable</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, gd32f4xx_misc.o(i.nvic_irq_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_config
</UL>

<P><STRONG><a name="[bd]"></a>nvic_priority_group_set</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_misc.o(i.nvic_priority_group_set))
<BR><BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_config
</UL>

<P><STRONG><a name="[8e]"></a>put_fat</STRONG> (Thumb, 310 bytes, Stack size 32 bytes, ff.o(i.put_fat))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>

<P><STRONG><a name="[eb]"></a>rcu_clock_freq_get</STRONG> (Thumb, 264 bytes, Stack size 84 bytes, gd32f4xx_rcu.o(i.rcu_clock_freq_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
</UL>

<P><STRONG><a name="[6b]"></a>rcu_periph_clock_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_config
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
</UL>

<P><STRONG><a name="[e7]"></a>rcu_periph_reset_disable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_disable))
<BR><BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_deinit
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
</UL>

<P><STRONG><a name="[e6]"></a>rcu_periph_reset_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_enable))
<BR><BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_deinit
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
</UL>

<P><STRONG><a name="[a0]"></a>sd_block_read</STRONG> (Thumb, 500 bytes, Stack size 40 bytes, sdcard.o(i.sd_block_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = sd_block_read &rArr; dma_receive_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_enable
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_disable
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_enable
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_disable
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_read
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_datablocksize_get
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>

<P><STRONG><a name="[a3]"></a>sd_block_write</STRONG> (Thumb, 760 bytes, Stack size 56 bytes, sdcard.o(i.sd_block_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = sd_block_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_enable
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_disable
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_enable
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_disable
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_write
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_datablocksize_get
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>

<P><STRONG><a name="[9e]"></a>sd_bus_mode_config</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, sdcard.o(i.sd_bus_mode_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = sd_bus_mode_config &rArr; sd_bus_width_config &rArr; sd_scr_get &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_hardware_clock_disable
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_clock_config
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_bus_mode_set
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[9b]"></a>sd_card_information_get</STRONG> (Thumb, 686 bytes, Stack size 12 bytes, sdcard.o(i.sd_card_information_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = sd_card_information_get
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[dc]"></a>sd_card_init</STRONG> (Thumb, 268 bytes, Stack size 16 bytes, sdcard.o(i.sd_card_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = sd_card_init &rArr; r6_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_power_state_get
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r6_error_check
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r2_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
</UL>

<P><STRONG><a name="[9c]"></a>sd_card_select_deselect</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, sdcard.o(i.sd_card_select_deselect))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = sd_card_select_deselect &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[9d]"></a>sd_cardstatus_get</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, sdcard.o(i.sd_cardstatus_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = sd_cardstatus_get &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[9a]"></a>sd_init</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, sdcard.o(i.sd_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = sd_init &rArr; sd_power_on &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_hardware_clock_disable
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_deinit
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_clock_config
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_bus_mode_set
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_config
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_config
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[6f]"></a>sd_interrupts_process</STRONG> (Thumb, 286 bytes, Stack size 8 bytes, sdcard.o(i.sd_interrupts_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = sd_interrupts_process &rArr; sd_transfer_stop &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_flag_get
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_flag_clear
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_disable
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_stop
</UL>
<BR>[Called By]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_IRQHandler
</UL>

<P><STRONG><a name="[a1]"></a>sd_multiblocks_read</STRONG> (Thumb, 632 bytes, Stack size 48 bytes, sdcard.o(i.sd_multiblocks_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = sd_multiblocks_read &rArr; dma_receive_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_enable
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_disable
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_enable
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_disable
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_read
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_datablocksize_get
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>

<P><STRONG><a name="[a4]"></a>sd_multiblocks_write</STRONG> (Thumb, 878 bytes, Stack size 56 bytes, sdcard.o(i.sd_multiblocks_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_enable
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_disable
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_enable
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_disable
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_write
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_datablocksize_get
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>

<P><STRONG><a name="[df]"></a>sd_power_on</STRONG> (Thumb, 290 bytes, Stack size 24 bytes, sdcard.o(i.sd_power_on))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = sd_power_on &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_power_state_set
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_hardware_clock_disable
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_clock_enable
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_clock_config
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_bus_mode_set
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r7_error_check
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r3_error_check
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdsent_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
</UL>

<P><STRONG><a name="[9f]"></a>sd_transfer_mode_config</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, sdcard.o(i.sd_transfer_mode_config))
<BR><BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[e1]"></a>sd_transfer_stop</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, sdcard.o(i.sd_transfer_stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = sd_transfer_stop &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_interrupts_process
</UL>

<P><STRONG><a name="[d9]"></a>sdio_bus_mode_set</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_bus_mode_set))
<BR><BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_mode_config
</UL>

<P><STRONG><a name="[d8]"></a>sdio_clock_config</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, gd32f4xx_sdio.o(i.sdio_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = sdio_clock_config
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_mode_config
</UL>

<P><STRONG><a name="[e5]"></a>sdio_clock_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[c0]"></a>sdio_command_index_get</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_command_index_get))
<BR><BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r6_error_check
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>

<P><STRONG><a name="[cd]"></a>sdio_command_response_config</STRONG> (Thumb, 52 bytes, Stack size 12 bytes, gd32f4xx_sdio.o(i.sdio_command_response_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = sdio_command_response_config
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_stop
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_select_deselect
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
</UL>

<P><STRONG><a name="[cf]"></a>sdio_csm_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_csm_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_stop
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_select_deselect
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
</UL>

<P><STRONG><a name="[c8]"></a>sdio_data_config</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, gd32f4xx_sdio.o(i.sdio_data_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = sdio_data_config
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
</UL>

<P><STRONG><a name="[d1]"></a>sdio_data_read</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_data_read))
<BR><BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
</UL>

<P><STRONG><a name="[c9]"></a>sdio_data_transfer_config</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_data_transfer_config))
<BR><BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
</UL>

<P><STRONG><a name="[d5]"></a>sdio_data_write</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_data_write))
<BR><BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
</UL>

<P><STRONG><a name="[de]"></a>sdio_deinit</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, gd32f4xx_sdio.o(i.sdio_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = sdio_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
</UL>

<P><STRONG><a name="[cb]"></a>sdio_dma_disable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_dma_disable))
<BR><BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[d3]"></a>sdio_dma_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_dma_enable))
<BR><BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[ca]"></a>sdio_dsm_disable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_dsm_disable))
<BR><BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[d0]"></a>sdio_dsm_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_dsm_enable))
<BR><BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
</UL>

<P><STRONG><a name="[8b]"></a>sdio_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r7_error_check
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r6_error_check
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r3_error_check
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r2_error_check
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdsent_error_check
</UL>

<P><STRONG><a name="[8a]"></a>sdio_flag_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdsent_error_check
</UL>

<P><STRONG><a name="[da]"></a>sdio_hardware_clock_disable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_hardware_clock_disable))
<BR><BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_mode_config
</UL>

<P><STRONG><a name="[e3]"></a>sdio_interrupt_disable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_interrupt_disable))
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_interrupts_process
</UL>

<P><STRONG><a name="[d2]"></a>sdio_interrupt_enable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[e2]"></a>sdio_interrupt_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_interrupts_process
</UL>

<P><STRONG><a name="[e0]"></a>sdio_interrupt_flag_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_interrupts_process
</UL>

<P><STRONG><a name="[dd]"></a>sdio_power_state_get</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_power_state_get))
<BR><BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
</UL>

<P><STRONG><a name="[e4]"></a>sdio_power_state_set</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_power_state_set))
<BR><BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[c1]"></a>sdio_response_get</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_response_get))
<BR><BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r6_error_check
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>

<P><STRONG><a name="[ce]"></a>sdio_wait_type_set</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_wait_type_set))
<BR><BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_stop
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_select_deselect
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
</UL>

<P><STRONG><a name="[73]"></a>systick_config</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, systick.o(i.systick_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = systick_config &rArr; NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[b8]"></a>usart_baudrate_set</STRONG> (Thumb, 224 bytes, Stack size 32 bytes, gd32f4xx_usart.o(i.usart_baudrate_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[ec]"></a>usart_data_receive</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_file
</UL>

<P><STRONG><a name="[b4]"></a>usart_data_transmit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[b7]"></a>usart_deinit</STRONG> (Thumb, 210 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[bb]"></a>usart_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_enable))
<BR><BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[b5]"></a>usart_flag_get</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_file
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[b9]"></a>usart_receive_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_receive_config))
<BR><BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[ba]"></a>usart_transmit_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_transmit_config))
<BR><BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[7b]"></a>write_file</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, function.o(i.write_file))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = write_file &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[ea]"></a>NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, systick.o(i.NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_config
</UL>

<P><STRONG><a name="[89]"></a>cmdsent_error_check</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, sdcard.o(i.cmdsent_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = cmdsent_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[a5]"></a>dma_receive_config</STRONG> (Thumb, 170 bytes, Stack size 64 bytes, sdcard.o(i.dma_receive_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = dma_receive_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_multi_data_mode_init
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flow_controller_config
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_clear
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_deinit
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_subperipheral_select
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[ad]"></a>dma_transfer_config</STRONG> (Thumb, 172 bytes, Stack size 64 bytes, sdcard.o(i.dma_transfer_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_multi_data_mode_init
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flow_controller_config
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_clear
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_deinit
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_subperipheral_select
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
</UL>

<P><STRONG><a name="[bc]"></a>gpio_config</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, sdcard.o(i.gpio_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = gpio_config &rArr; gpio_af_set
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
</UL>

<P><STRONG><a name="[bf]"></a>r1_error_check</STRONG> (Thumb, 120 bytes, Stack size 24 bytes, sdcard.o(i.r1_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_index_get
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_type_check
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_stop
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_select_deselect
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
</UL>

<P><STRONG><a name="[c2]"></a>r1_error_type_check</STRONG> (Thumb, 174 bytes, Stack size 0 bytes, sdcard.o(i.r1_error_type_check))
<BR><BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>

<P><STRONG><a name="[c3]"></a>r2_error_check</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, sdcard.o(i.r2_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = r2_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
</UL>

<P><STRONG><a name="[c4]"></a>r3_error_check</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, sdcard.o(i.r3_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = r3_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[c5]"></a>r6_error_check</STRONG> (Thumb, 158 bytes, Stack size 24 bytes, sdcard.o(i.r6_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = r6_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_index_get
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
</UL>

<P><STRONG><a name="[c6]"></a>r7_error_check</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, sdcard.o(i.r7_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = r7_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[c7]"></a>rcu_config</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, sdcard.o(i.rcu_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rcu_config
</UL>
<BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
</UL>

<P><STRONG><a name="[d7]"></a>sd_bus_width_config</STRONG> (Thumb, 242 bytes, Stack size 16 bytes, sdcard.o(i.sd_bus_width_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = sd_bus_width_config &rArr; sd_scr_get &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_mode_config
</UL>

<P><STRONG><a name="[d6]"></a>sd_card_state_get</STRONG> (Thumb, 166 bytes, Stack size 24 bytes, sdcard.o(i.sd_card_state_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = sd_card_state_get &rArr; sdio_command_response_config
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_index_get
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_type_check
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
</UL>

<P><STRONG><a name="[cc]"></a>sd_datablocksize_get</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, sdcard.o(i.sd_datablocksize_get))
<BR><BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[db]"></a>sd_scr_get</STRONG> (Thumb, 344 bytes, Stack size 32 bytes, sdcard.o(i.sd_scr_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = sd_scr_get &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_read
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
</UL>

<P><STRONG><a name="[e9]"></a>system_clock_240m_25m_hxtal</STRONG> (Thumb, 250 bytes, Stack size 0 bytes, system_gd32f4xx.o(i.system_clock_240m_25m_hxtal))
<BR><BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>

<P><STRONG><a name="[71]"></a>system_clock_config</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.system_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_240m_25m_hxtal
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[85]"></a>check_fs</STRONG> (Thumb, 138 bytes, Stack size 16 bytes, ff.o(i.check_fs))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = check_fs &rArr; disk_read &rArr; sd_multiblocks_read &rArr; dma_receive_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
</UL>

<P><STRONG><a name="[91]"></a>chk_chr</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, ff.o(i.chk_chr))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>

<P><STRONG><a name="[87]"></a>chk_mounted</STRONG> (Thumb, 898 bytes, Stack size 80 bytes, ff.o(i.chk_mounted))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = chk_mounted &rArr; disk_initialize &rArr; sd_bus_mode_config &rArr; sd_bus_width_config &rArr; sd_scr_get &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[8c]"></a>create_chain</STRONG> (Thumb, 202 bytes, Stack size 32 bytes, ff.o(i.create_chain))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>

<P><STRONG><a name="[8f]"></a>create_name</STRONG> (Thumb, 336 bytes, Stack size 48 bytes, ff.o(i.create_name))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = create_name &rArr; mem_set
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_chr
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
</UL>

<P><STRONG><a name="[92]"></a>dir_find</STRONG> (Thumb, 92 bytes, Stack size 24 bytes, ff.o(i.dir_find))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cmp
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
</UL>

<P><STRONG><a name="[96]"></a>dir_next</STRONG> (Thumb, 280 bytes, Stack size 24 bytes, ff.o(i.dir_next))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[98]"></a>dir_register</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, ff.o(i.dir_register))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = dir_register &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[93]"></a>dir_sdi</STRONG> (Thumb, 156 bytes, Stack size 24 bytes, ff.o(i.dir_sdi))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = dir_sdi &rArr; get_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[af]"></a>follow_path</STRONG> (Thumb, 158 bytes, Stack size 32 bytes, ff.o(i.follow_path))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[95]"></a>mem_cmp</STRONG> (Thumb, 38 bytes, Stack size 20 bytes, ff.o(i.mem_cmp))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = mem_cmp
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[99]"></a>mem_cpy</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, ff.o(i.mem_cpy))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = mem_cpy
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[90]"></a>mem_set</STRONG> (Thumb, 20 bytes, Stack size 12 bytes, ff.o(i.mem_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = mem_set
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>

<P><STRONG><a name="[94]"></a>move_window</STRONG> (Thumb, 114 bytes, Stack size 24 bytes, ff.o(i.move_window))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[b1]"></a>remove_chain</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, ff.o(i.remove_chain))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = remove_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[b3]"></a>sync</STRONG> (Thumb, 202 bytes, Stack size 16 bytes, ff.o(i.sync))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = sync &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_ioctl
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
</UL>

<P><STRONG><a name="[b2]"></a>validate</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, ff.o(i.validate))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = validate
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
</UL>

<P><STRONG><a name="[83]"></a>_printf_core</STRONG> (Thumb, 436 bytes, Stack size 96 bytes, printf3.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf$3
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
