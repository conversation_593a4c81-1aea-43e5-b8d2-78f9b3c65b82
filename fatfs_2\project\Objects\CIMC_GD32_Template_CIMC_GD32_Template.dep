Dependencies for Project 'CIMC_GD32_Template', Target 'CIMC_GD32_Template': (DO NOT MODIFY !)
F (..\User\gd32f4xx_it.c)(0x6811999C)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_it.o --omf_browse .\objects\gd32f4xx_it.crf --depend .\objects\gd32f4xx_it.d)
I (..\User\gd32f4xx_it.h)(0x61ADC345)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
I (..\User\systick.h)(0x61ADC343)
I (..\HardWare\SDCARD\sdcard.h)(0x68119038)
F (..\User\main.c)(0x67DE7C80)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\main.o --omf_browse .\objects\main.crf --depend .\objects\main.d)
I (..\HeaderFiles\HeaderFiles.h)(0x67DF6F11)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
I (..\User\systick.h)(0x61ADC343)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x599ECD2C)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x599ECD2C)
I (..\Function\Function.h)(0x67FCB569)
F (..\User\systick.c)(0x61ADC343)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\systick.o --omf_browse .\objects\systick.crf --depend .\objects\systick.d)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
I (..\User\systick.h)(0x61ADC343)
F (..\HardWare\LED\LED.c)(0x67DE7F00)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\led.o --omf_browse .\objects\led.crf --depend .\objects\led.d)
I (..\HardWare\LED\LED.h)(0x67DE7DB1)
I (..\HeaderFiles\HeaderFiles.h)(0x67DF6F11)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
I (..\User\systick.h)(0x61ADC343)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x599ECD2C)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x599ECD2C)
I (..\Function\Function.h)(0x67FCB569)
F (..\HardWare\USART\USART.c)(0x6819B052)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\usart.o --omf_browse .\objects\usart.crf --depend .\objects\usart.d)
I (..\HardWare\USART\USART.h)(0x67FCB46D)
I (..\HeaderFiles\HeaderFiles.h)(0x67DF6F11)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
I (..\User\systick.h)(0x61ADC343)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x599ECD2C)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x599ECD2C)
I (..\Function\Function.h)(0x67FCB569)
F (..\HardWare\SDCARD\sdcard.c)(0x5E86E15D)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\sdcard.o --omf_browse .\objects\sdcard.crf --depend .\objects\sdcard.d)
I (..\HardWare\SDCARD\sdcard.h)(0x68119038)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x599ECD2C)
F (..\Function\Function.c)(0x68218A89)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\function.o --omf_browse .\objects\function.crf --depend .\objects\function.d)
I (..\Function\Function.h)(0x67FCB569)
I (..\HeaderFiles\HeaderFiles.h)(0x67DF6F11)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
I (..\User\systick.h)(0x61ADC343)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x599ECD2C)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x599ECD2C)
I (..\HardWare\LED\LED.h)(0x67DE7DB1)
I (..\HardWare\RTC\RTC.h)(0x67FCB4DC)
I (..\HardWare\USART\USART.h)(0x67FCB46D)
I (..\HardWare\SPI_FLASH\SPI_FLASH.h)(0x67FE2ADD)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x4E64F36A)
I (..\Fatfs\diskio.h)(0x4D21CB12)
I (..\HardWare\SDCARD\sdcard.h)(0x68119038)
F (..\HeaderFiles\HeaderFiles.h)(0x67DF6F11)()
F (..\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c)(0x67D91E03)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\system_gd32f4xx.o --omf_browse .\objects\system_gd32f4xx.crf --depend .\objects\system_gd32f4xx.d)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_adc.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_adc.o --omf_browse .\objects\gd32f4xx_adc.crf --depend .\objects\gd32f4xx_adc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_can.c)(0x67D917C0)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_can.o --omf_browse .\objects\gd32f4xx_can.crf --depend .\objects\gd32f4xx_can.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_crc.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_crc.o --omf_browse .\objects\gd32f4xx_crc.crf --depend .\objects\gd32f4xx_crc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ctc.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_ctc.o --omf_browse .\objects\gd32f4xx_ctc.crf --depend .\objects\gd32f4xx_ctc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dac.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_dac.o --omf_browse .\objects\gd32f4xx_dac.crf --depend .\objects\gd32f4xx_dac.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dbg.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_dbg.o --omf_browse .\objects\gd32f4xx_dbg.crf --depend .\objects\gd32f4xx_dbg.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dci.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_dci.o --omf_browse .\objects\gd32f4xx_dci.crf --depend .\objects\gd32f4xx_dci.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dma.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_dma.o --omf_browse .\objects\gd32f4xx_dma.crf --depend .\objects\gd32f4xx_dma.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_enet.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_enet.o --omf_browse .\objects\gd32f4xx_enet.crf --depend .\objects\gd32f4xx_enet.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exmc.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_exmc.o --omf_browse .\objects\gd32f4xx_exmc.crf --depend .\objects\gd32f4xx_exmc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exti.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_exti.o --omf_browse .\objects\gd32f4xx_exti.crf --depend .\objects\gd32f4xx_exti.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fmc.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_fmc.o --omf_browse .\objects\gd32f4xx_fmc.crf --depend .\objects\gd32f4xx_fmc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fwdgt.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_fwdgt.o --omf_browse .\objects\gd32f4xx_fwdgt.crf --depend .\objects\gd32f4xx_fwdgt.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_gpio.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_gpio.o --omf_browse .\objects\gd32f4xx_gpio.crf --depend .\objects\gd32f4xx_gpio.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_i2c.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_i2c.o --omf_browse .\objects\gd32f4xx_i2c.crf --depend .\objects\gd32f4xx_i2c.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ipa.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_ipa.o --omf_browse .\objects\gd32f4xx_ipa.crf --depend .\objects\gd32f4xx_ipa.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_iref.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_iref.o --omf_browse .\objects\gd32f4xx_iref.crf --depend .\objects\gd32f4xx_iref.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_misc.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_misc.o --omf_browse .\objects\gd32f4xx_misc.crf --depend .\objects\gd32f4xx_misc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_pmu.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_pmu.o --omf_browse .\objects\gd32f4xx_pmu.crf --depend .\objects\gd32f4xx_pmu.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rcu.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_rcu.o --omf_browse .\objects\gd32f4xx_rcu.crf --depend .\objects\gd32f4xx_rcu.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rtc.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_rtc.o --omf_browse .\objects\gd32f4xx_rtc.crf --depend .\objects\gd32f4xx_rtc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_sdio.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_sdio.o --omf_browse .\objects\gd32f4xx_sdio.crf --depend .\objects\gd32f4xx_sdio.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_spi.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_spi.o --omf_browse .\objects\gd32f4xx_spi.crf --depend .\objects\gd32f4xx_spi.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_syscfg.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_syscfg.o --omf_browse .\objects\gd32f4xx_syscfg.crf --depend .\objects\gd32f4xx_syscfg.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_timer.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_timer.o --omf_browse .\objects\gd32f4xx_timer.crf --depend .\objects\gd32f4xx_timer.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_tli.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_tli.o --omf_browse .\objects\gd32f4xx_tli.crf --depend .\objects\gd32f4xx_tli.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_trng.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_trng.o --omf_browse .\objects\gd32f4xx_trng.crf --depend .\objects\gd32f4xx_trng.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_usart.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_usart.o --omf_browse .\objects\gd32f4xx_usart.crf --depend .\objects\gd32f4xx_usart.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_wwdgt.c)(0x65A7AAB6)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_wwdgt.o --omf_browse .\objects\gd32f4xx_wwdgt.crf --depend .\objects\gd32f4xx_wwdgt.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
F (..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s)(0x65A7AAB5)(--cpu Cortex-M4.fp -g --apcs=interwork --pd "__MICROLIB SETA 1"

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

--pd "__UVISION_VERSION SETA 525" --pd "_RTE_ SETA 1" --pd "GD32F470 SETA 1"

--list .\listings\startup_gd32f450_470.lst --xref -o .\objects\startup_gd32f450_470.o --depend .\objects\startup_gd32f450_470.d)
F (..\Fatfs\diskio.c)(0x5C8751B3)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\diskio.o --omf_browse .\objects\diskio.crf --depend .\objects\diskio.d)
I (..\Fatfs\diskio.h)(0x4D21CB12)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\HardWare\SDCARD\sdcard.h)(0x68119038)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AC)
I (..\CMSIS\core_cm4.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB5)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB5)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB5)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB5)
I (..\User\gd32f4xx_libopt.h)(0x67D91959)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91242)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB5)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB5)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x599ECD2C)
F (..\Fatfs\ff.c)(0x4E64E464)(--c99 --gnu -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare\RTC -I ..\HardWare\USART -I ..\HardWare\SPI_FLASH -I ..\HardWare\SDCARD -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\ff.o --omf_browse .\objects\ff.crf --depend .\objects\ff.d)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x4E64F36A)
I (..\Fatfs\diskio.h)(0x4D21CB12)
F (..\01 Readme\Readme.txt)(0x67DF6EFA)()
