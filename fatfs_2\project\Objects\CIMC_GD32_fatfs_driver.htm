<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\CIMC_GD32_fatfs_driver.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\CIMC_GD32_fatfs_driver.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060750: Last Updated: Mon Jun 16 12:41:18 2025
<BR><P>
<H3>Maximum Stack Usage =        928 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; UsrFunction &rArr; update_sampling &rArr; write_overlimit_data &rArr; check_and_create_new_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1c]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1c]">ADC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1c]">ADC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4]">BusFault_Handler</a> from gd32f4xx_it.o(i.BusFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[20]">CAN0_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1e]">CAN0_RX0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1f]">CAN0_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1d]">CAN0_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4c]">CAN1_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4a]">CAN1_RX0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4b]">CAN1_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[49]">CAN1_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[58]">DCI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[15]">DMA0_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[16]">DMA0_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[17]">DMA0_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[18]">DMA0_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[19]">DMA0_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1a]">DMA0_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1b]">DMA0_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[39]">DMA0_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[42]">DMA1_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[43]">DMA1_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[44]">DMA1_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[45]">DMA1_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[46]">DMA1_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4e]">DMA1_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4f]">DMA1_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[50]">DMA1_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[7]">DebugMon_Handler</a> from gd32f4xx_it.o(i.DebugMon_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[47]">ENET_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[48]">ENET_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3a]">EXMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[10]">EXTI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[32]">EXTI10_15_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[11]">EXTI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[12]">EXTI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[13]">EXTI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[14]">EXTI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[21]">EXTI5_9_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[e]">FMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5a]">FPU_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from gd32f4xx_it.o(i.HardFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2a]">I2C0_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[29]">I2C0_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2c]">I2C1_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2b]">I2C1_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[53]">I2C2_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[52]">I2C2_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[62]">IPA_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[b]">LVD_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3]">MemManage_Handler</a> from gd32f4xx_it.o(i.MemManage_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from gd32f4xx_it.o(i.NMI_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[8]">PendSV_Handler</a> from gd32f4xx_it.o(i.PendSV_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[f]">RCU_CTC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[33]">RTC_Alarm_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[d]">RTC_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3b]">SDIO_IRQHandler</a> from gd32f4xx_it.o(i.SDIO_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2d]">SPI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2e]">SPI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3d]">SPI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5d]">SPI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5e]">SPI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5f]">SPI5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6]">SVC_Handler</a> from gd32f4xx_it.o(i.SVC_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[9]">SysTick_Handler</a> from gd32f4xx_it.o(i.SysTick_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[64]">SystemInit</a> from system_gd32f4xx.o(i.SystemInit) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[c]">TAMPER_STAMP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[22]">TIMER0_BRK_TIMER8_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[25]">TIMER0_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[24]">TIMER0_TRG_CMT_TIMER10_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[23]">TIMER0_UP_TIMER9_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[26]">TIMER1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[27]">TIMER2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[28]">TIMER3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3c]">TIMER4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[40]">TIMER5_DAC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[41]">TIMER6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[35]">TIMER7_BRK_TIMER11_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[38]">TIMER7_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[37]">TIMER7_TRG_CMT_TIMER13_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[36]">TIMER7_UP_TIMER12_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[61]">TLI_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[60]">TLI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[59]">TRNG_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3e]">UART3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3f]">UART4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5b]">UART6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5c]">UART7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2f]">USART0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[30]">USART1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[31]">USART2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[51]">USART5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4d]">USBFS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[34]">USBFS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[55]">USBHS_EP1_In_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[54]">USBHS_EP1_Out_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[57]">USBHS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[56]">USBHS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5]">UsageFault_Handler</a> from gd32f4xx_it.o(i.UsageFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[a]">WWDGT_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[65]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[67]">_sbackspace</a> from _sgetc.o(.text) referenced 2 times from __0sscanf.o(.text)
 <LI><a href="#[68]">_scanf_char_input</a> from scanf_char.o(.text) referenced from scanf_char.o(.text)
 <LI><a href="#[66]">_sgetc</a> from _sgetc.o(.text) referenced 2 times from __0sscanf.o(.text)
 <LI><a href="#[6b]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0sprintf)
 <LI><a href="#[6a]">fputc</a> from usart.o(i.fputc) referenced from printfa.o(i.__0printf)
 <LI><a href="#[69]">isspace</a> from isspace_o.o(.text) referenced 2 times from scanf_char.o(.text)
 <LI><a href="#[63]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[65]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[193]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[6c]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[8c]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[194]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[195]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[196]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[197]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[198]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>CAN0_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>CAN0_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>CAN0_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>CAN0_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>CAN1_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>DCI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>DMA0_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>DMA0_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA0_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA0_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA0_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA0_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA0_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>DMA0_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>DMA1_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>ENET_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>ENET_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>EXMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>EXTI10_15_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>EXTI5_9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>I2C0_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>I2C0_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>IPA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>LVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>RCU_CTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>TAMPER_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>TIMER0_BRK_TIMER8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIMER0_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIMER0_TRG_CMT_TIMER10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>TIMER0_UP_TIMER9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIMER1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIMER2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIMER3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>TIMER4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>TIMER5_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>TIMER6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>TIMER7_BRK_TIMER11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIMER7_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIMER7_TRG_CMT_TIMER13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TIMER7_UP_TIMER12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>TLI_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>TLI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>TRNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>UART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>UART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>USART0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>USART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>USBFS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>USBFS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>USBHS_EP1_In_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>USBHS_EP1_Out_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>USBHS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>USBHS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>WWDGT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6f]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[199]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[19a]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[6e]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[d0]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_config_command
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_device_id_from_flash
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_data_storage
</UL>

<P><STRONG><a name="[19b]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[70]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[160]"></a>strstr</STRONG> (Thumb, 36 bytes, Stack size 12 bytes, strstr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strstr
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_config_from_tf
</UL>

<P><STRONG><a name="[14b]"></a>strncpy</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, strncpy.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strncpy
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_time_input
</UL>

<P><STRONG><a name="[dc]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_sample_data
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_overlimit_data
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_log_data
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_ratio_value
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_limit_value
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_device_id_to_flash
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_device_id_from_flash
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_time_input
</UL>

<P><STRONG><a name="[14c]"></a>strcmp</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, strcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[fa]"></a>strncmp</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, strncmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strncmp
</UL>
<BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_device_id_from_flash
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_command_match
</UL>

<P><STRONG><a name="[71]"></a>__0sscanf</STRONG> (Thumb, 48 bytes, Stack size 72 bytes, __0sscanf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = __0sscanf &rArr; __vfscanf_char &rArr; __vfscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf_char
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_time_input
</UL>

<P><STRONG><a name="[73]"></a>_scanf_int</STRONG> (Thumb, 332 bytes, Stack size 56 bytes, _scanf_int.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _scanf_int
</UL>
<BR>[Calls]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_chval
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>

<P><STRONG><a name="[75]"></a>atoi</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, atoi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = atoi &rArr; strtol &rArr; _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_time_input
</UL>

<P><STRONG><a name="[104]"></a>__aeabi_f2d</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_overlimit_data
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_sampling
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_oled_display
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_ratio_value
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_limit_value
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_config_from_tf
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_config_from_flash
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_command
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_read_command
</UL>

<P><STRONG><a name="[19c]"></a>__aeabi_cdcmpeq</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, cdcmple.o(.text), UNUSED)

<P><STRONG><a name="[141]"></a>__aeabi_cdcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_ratio_value
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_limit_value
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_config_from_flash
</UL>

<P><STRONG><a name="[ed]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_ratio_value
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_limit_value
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_config_from_flash
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[78]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_ratio_value
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_limit_value
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_config_from_tf
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[19d]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[f0]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[7a]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[74]"></a>_chval</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, _chval.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_int
</UL>

<P><STRONG><a name="[72]"></a>__vfscanf_char</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, scanf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = __vfscanf_char &rArr; __vfscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
</UL>

<P><STRONG><a name="[66]"></a>_sgetc</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 2]<UL><LI> __0sscanf.o(.text)
<LI> strtod.o(.text)
</UL>
<P><STRONG><a name="[67]"></a>_sbackspace</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 2]<UL><LI> __0sscanf.o(.text)
<LI> strtod.o(.text)
</UL>
<P><STRONG><a name="[80]"></a>__strtod_int</STRONG> (Thumb, 90 bytes, Stack size 40 bytes, strtod.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_local_sscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
</UL>

<P><STRONG><a name="[77]"></a>strtol</STRONG> (Thumb, 112 bytes, Stack size 32 bytes, strtol.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = strtol &rArr; _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
</UL>

<P><STRONG><a name="[19e]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[79]"></a>_float_round</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[19f]"></a>_float_epilogue</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, fepilogue.o(.text), UNUSED)

<P><STRONG><a name="[83]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[87]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[88]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[89]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[8a]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[8b]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[6d]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[1a0]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[7c]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[1a1]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[7b]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[1a2]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[84]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[1a3]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[81]"></a>__rt_ctype_table</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, ctype_o.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;isspace
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
</UL>

<P><STRONG><a name="[69]"></a>isspace</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, isspace_o.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = isspace
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
</UL>
<BR>[Address Reference Count : 2]<UL><LI> scanf_char.o(.text)
<LI> strtod.o(.text)
</UL>
<P><STRONG><a name="[7d]"></a>__vfscanf</STRONG> (Thumb, 808 bytes, Stack size 88 bytes, _scanf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = __vfscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_real
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_int
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf_char
</UL>

<P><STRONG><a name="[7f]"></a>_scanf_real</STRONG> (Thumb, 0 bytes, Stack size 104 bytes, scanf_fp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = _scanf_real
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_local_sscanf
</UL>

<P><STRONG><a name="[8f]"></a>_scanf_really_real</STRONG> (Thumb, 556 bytes, Stack size 104 bytes, scanf_fp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[82]"></a>_strtoul</STRONG> (Thumb, 158 bytes, Stack size 40 bytes, _strtoul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_chval
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
</UL>

<P><STRONG><a name="[86]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[85]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[8e]"></a>__aeabi_ul2d</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, dfltul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[91]"></a>ADC_Init</STRONG> (Thumb, 86 bytes, Stack size 8 bytes, adc.o(i.ADC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = ADC_Init &rArr; adc_routine_channel_config
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_special_function_config
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_routine_channel_config
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_external_trigger_source_config
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_external_trigger_config
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_enable
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_deinit
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_data_alignment_config
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_channel_length_config
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_calibration_enable
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_port_init
</UL>

<P><STRONG><a name="[9c]"></a>ADC_Read_Value</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, adc.o(i.ADC_Read_Value))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC_Read_Value
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_software_trigger_enable
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_routine_data_read
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_flag_get
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Read_Voltage
</UL>

<P><STRONG><a name="[a1]"></a>ADC_Read_Voltage</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, adc.o(i.ADC_Read_Voltage))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ADC_Read_Voltage &rArr; ADC_Read_Value
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Read_Value
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_adc_voltage
</UL>

<P><STRONG><a name="[a2]"></a>ADC_port_init</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, adc.o(i.ADC_port_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = ADC_port_init &rArr; ADC_Init &rArr; adc_routine_channel_config
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_software_trigger_enable
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_clock_config
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[4]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[a6]"></a>I2C_Start</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, oled.o(i.I2C_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = I2C_Start &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>

<P><STRONG><a name="[ab]"></a>I2C_Stop</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, oled.o(i.I2C_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = I2C_Stop &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitAck
</UL>

<P><STRONG><a name="[ac]"></a>I2C_WaitAck</STRONG> (Thumb, 116 bytes, Stack size 8 bytes, oled.o(i.I2C_WaitAck))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = I2C_WaitAck &rArr; I2C_Stop &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_delay
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Stop
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>

<P><STRONG><a name="[a9]"></a>IIC_delay</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, oled.o(i.IIC_delay))
<BR><BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_Byte
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitAck
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Stop
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Start
</UL>

<P><STRONG><a name="[ae]"></a>KEY_Init</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, key.o(i.KEY_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = KEY_Init &rArr; gpio_mode_set
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[af]"></a>LED_Init</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, led.o(i.LED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = LED_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[3]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[b0]"></a>OLED_Clear</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, oled.o(i.OLED_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = OLED_Clear &rArr; OLED_Refresh &rArr; OLED_WR_Byte &rArr; I2C_WaitAck &rArr; I2C_Stop &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Refresh
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_oled_display
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[b6]"></a>OLED_ClearPoint</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, oled.o(i.OLED_ClearPoint))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = OLED_ClearPoint
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>

<P><STRONG><a name="[b5]"></a>OLED_DrawPoint</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, oled.o(i.OLED_DrawPoint))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = OLED_DrawPoint
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>

<P><STRONG><a name="[b2]"></a>OLED_Init</STRONG> (Thumb, 324 bytes, Stack size 8 bytes, oled.o(i.OLED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = OLED_Init &rArr; OLED_Clear &rArr; OLED_Refresh &rArr; OLED_WR_Byte &rArr; I2C_WaitAck &rArr; I2C_Stop &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[b1]"></a>OLED_Refresh</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, oled.o(i.OLED_Refresh))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = OLED_Refresh &rArr; OLED_WR_Byte &rArr; I2C_WaitAck &rArr; I2C_Stop &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_oled_display
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>

<P><STRONG><a name="[b4]"></a>OLED_ShowChar</STRONG> (Thumb, 240 bytes, Stack size 56 bytes, oled.o(i.OLED_ShowChar))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = OLED_ShowChar &rArr; OLED_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_DrawPoint
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ClearPoint
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
</UL>

<P><STRONG><a name="[b7]"></a>OLED_ShowString</STRONG> (Thumb, 66 bytes, Stack size 20 bytes, oled.o(i.OLED_ShowString))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_oled_display
</UL>

<P><STRONG><a name="[b3]"></a>OLED_WR_Byte</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, oled.o(i.OLED_WR_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = OLED_WR_Byte &rArr; I2C_WaitAck &rArr; I2C_Stop &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_Byte
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitAck
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Stop
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Refresh
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[8]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>SDIO_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.SDIO_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SDIO_IRQHandler &rArr; sd_interrupts_process &rArr; sd_transfer_stop &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_interrupts_process
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[b8]"></a>Send_Byte</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, oled.o(i.Send_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>

<P><STRONG><a name="[9]"></a>SysTick_Handler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTick_Handler
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_decrement
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>SystemInit</STRONG> (Thumb, 194 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SystemInit &rArr; system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[bc]"></a>System_Init</STRONG> (Thumb, 156 bytes, Stack size 8 bytes, function.o(i.System_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = System_Init &rArr; read_device_id_from_flash &rArr; save_device_id_to_flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_transmit_config
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_receive_config
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_enable
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_config
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_device_id_from_flash
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[c7]"></a>UsrFunction</STRONG> (Thumb, 1594 bytes, Stack size 32 bytes, function.o(i.UsrFunction))
<BR><BR>[Stack]<UL><LI>Max Depth = 920<LI>Call Chain = UsrFunction &rArr; update_sampling &rArr; write_overlimit_data &rArr; check_and_create_new_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_port_init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_toggle
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_file
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_sampling
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;unhide_command
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_self_check
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stop_sampling
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_sampling
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_config
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memory_compare
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_rtc_init
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_config_from_flash
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_data_storage
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;increment_boot_count
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hide_command
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_self_check
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_key234_press
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_key1_press
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_command_match
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Refresh
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Init
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e6]"></a>__0printf</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[1a4]"></a>__1printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[c4]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_sample_data
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_overlimit_data
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_log_data
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_file
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_sampling
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;unhide_command
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_self_check
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stop_sampling
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_sampling
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_ratio_value
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_limit_value
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_status_check
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_reset_command
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_now_command
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_config_command
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_device_id_from_flash
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_config_from_tf
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hide_command
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_test_write_read
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_self_check
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_command
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_read_command
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_and_create_new_file
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adjust_sample_cycle
</UL>

<P><STRONG><a name="[1a5]"></a>__c89printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[1a6]"></a>printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[e8]"></a>__0sprintf</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[1a7]"></a>__1sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[12c]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_overlimit_data
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_log_data
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_sampling
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_oled_display
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_datetime_string
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_current_time_string
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_sample_filename
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_overlimit_filename
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_log_filename
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_hide_filename
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encode_voltage_hex
</UL>

<P><STRONG><a name="[1a8]"></a>__c89sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[1a9]"></a>sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[76]"></a>__aeabi_errno_addr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, errno.o(i.__aeabi_errno_addr))
<BR><BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
</UL>

<P><STRONG><a name="[1aa]"></a>__rt_errno_addr</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, errno.o(i.__aeabi_errno_addr), UNUSED)

<P><STRONG><a name="[e9]"></a>__hardfp_atof</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, atof.o(i.__hardfp_atof))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = __hardfp_atof &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strtod_int
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__read_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_ratio_value
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_limit_value
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_config_from_tf
</UL>

<P><STRONG><a name="[ea]"></a>__read_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__read_errno))
<BR><BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
</UL>

<P><STRONG><a name="[1ab]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[1ac]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[1ad]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[eb]"></a>__set_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__set_errno))
<BR><BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
</UL>

<P><STRONG><a name="[90]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, scanf_fp.o(i._is_digit), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[9b]"></a>adc_calibration_enable</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_calibration_enable))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>

<P><STRONG><a name="[95]"></a>adc_channel_length_config</STRONG> (Thumb, 82 bytes, Stack size 12 bytes, gd32f4xx_adc.o(i.adc_channel_length_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = adc_channel_length_config
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>

<P><STRONG><a name="[a5]"></a>adc_clock_config</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_clock_config))
<BR><BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_port_init
</UL>

<P><STRONG><a name="[94]"></a>adc_data_alignment_config</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_data_alignment_config))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>

<P><STRONG><a name="[92]"></a>adc_deinit</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, gd32f4xx_adc.o(i.adc_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = adc_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>

<P><STRONG><a name="[99]"></a>adc_enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_enable))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>

<P><STRONG><a name="[98]"></a>adc_external_trigger_config</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_external_trigger_config))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>

<P><STRONG><a name="[97]"></a>adc_external_trigger_source_config</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_external_trigger_source_config))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>

<P><STRONG><a name="[a0]"></a>adc_flag_clear</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Read_Value
</UL>

<P><STRONG><a name="[9e]"></a>adc_flag_get</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Read_Value
</UL>

<P><STRONG><a name="[96]"></a>adc_routine_channel_config</STRONG> (Thumb, 172 bytes, Stack size 20 bytes, gd32f4xx_adc.o(i.adc_routine_channel_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = adc_routine_channel_config
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>

<P><STRONG><a name="[9f]"></a>adc_routine_data_read</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_routine_data_read))
<BR><BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Read_Value
</UL>

<P><STRONG><a name="[9d]"></a>adc_software_trigger_enable</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_software_trigger_enable))
<BR><BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_port_init
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Read_Value
</UL>

<P><STRONG><a name="[93]"></a>adc_special_function_config</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_special_function_config))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>

<P><STRONG><a name="[f3]"></a>adjust_sample_cycle</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, function.o(i.adjust_sample_cycle))
<BR><BR>[Stack]<UL><LI>Max Depth = 156<LI>Call Chain = adjust_sample_cycle &rArr; update_oled_display &rArr; OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_oled_display
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_config_to_flash
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_key234_press
</UL>

<P><STRONG><a name="[f6]"></a>check_and_create_new_file</STRONG> (Thumb, 232 bytes, Stack size 80 bytes, function.o(i.check_and_create_new_file))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = check_and_create_new_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_sample_filename
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_overlimit_filename
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_hide_filename
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_sample_data
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_overlimit_data
</UL>

<P><STRONG><a name="[d3]"></a>check_command_match</STRONG> (Thumb, 164 bytes, Stack size 16 bytes, function.o(i.check_command_match))
<BR><BR>[Stack]<UL><LI>Max Depth = 520<LI>Call Chain = check_command_match &rArr; unhide_command &rArr; write_log_data &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;unhide_command
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stop_sampling
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_sampling
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hide_command
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[e2]"></a>check_key1_press</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, function.o(i.check_key1_press))
<BR><BR>[Stack]<UL><LI>Max Depth = 520<LI>Call Chain = check_key1_press &rArr; toggle_sampling_by_key &rArr; stop_sampling &rArr; write_log_data &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;toggle_sampling_by_key
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[e3]"></a>check_key234_press</STRONG> (Thumb, 184 bytes, Stack size 16 bytes, function.o(i.check_key234_press))
<BR><BR>[Stack]<UL><LI>Max Depth = 172<LI>Call Chain = check_key234_press &rArr; adjust_sample_cycle &rArr; update_oled_display &rArr; OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adjust_sample_cycle
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[114]"></a>clust2sect</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, ff.o(i.clust2sect))
<BR><BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>

<P><STRONG><a name="[103]"></a>config_read_command</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, function.o(i.config_read_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = config_read_command &rArr; load_config_from_flash &rArr; spi_flash_buffer_read &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_config_from_flash
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[105]"></a>config_save_command</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, function.o(i.config_save_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 512<LI>Call Chain = config_save_command &rArr; write_log_data &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_log_data
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_config_to_flash
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[154]"></a>convert_to_bcd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, function.o(i.convert_to_bcd))
<BR><BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_config_command
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[10a]"></a>create_directories</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, function.o(i.create_directories))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = create_directories &rArr; f_mkdir &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_data_storage
</UL>

<P><STRONG><a name="[9a]"></a>delay_1ms</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, systick.o(i.delay_1ms))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[ba]"></a>delay_decrement</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, systick.o(i.delay_decrement))
<BR><BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[cd]"></a>disk_initialize</STRONG> (Thumb, 134 bytes, Stack size 88 bytes, diskio.o(i.disk_initialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = disk_initialize &rArr; sd_bus_mode_config &rArr; sd_bus_width_config &rArr; sd_scr_get &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_mode_config
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_select_deselect
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_information_get
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_mode_config
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_self_check
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
</UL>

<P><STRONG><a name="[18d]"></a>disk_ioctl</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, diskio.o(i.disk_ioctl))
<BR><BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
</UL>

<P><STRONG><a name="[fc]"></a>disk_read</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, diskio.o(i.disk_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = disk_read &rArr; sd_multiblocks_read &rArr; dma_receive_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
</UL>

<P><STRONG><a name="[ff]"></a>disk_status</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, diskio.o(i.disk_status))
<BR><BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
</UL>

<P><STRONG><a name="[11f]"></a>disk_write</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, diskio.o(i.disk_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>

<P><STRONG><a name="[124]"></a>dma_channel_disable</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_channel_disable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_channel_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>

<P><STRONG><a name="[129]"></a>dma_channel_enable</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_channel_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_channel_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>

<P><STRONG><a name="[128]"></a>dma_channel_subperipheral_select</STRONG> (Thumb, 38 bytes, Stack size 12 bytes, gd32f4xx_dma.o(i.dma_channel_subperipheral_select))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = dma_channel_subperipheral_select
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>

<P><STRONG><a name="[125]"></a>dma_deinit</STRONG> (Thumb, 166 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_deinit
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>

<P><STRONG><a name="[123]"></a>dma_flag_clear</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_flag_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>

<P><STRONG><a name="[172]"></a>dma_flag_get</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[127]"></a>dma_flow_controller_config</STRONG> (Thumb, 64 bytes, Stack size 12 bytes, gd32f4xx_dma.o(i.dma_flow_controller_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = dma_flow_controller_config
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>

<P><STRONG><a name="[126]"></a>dma_multi_data_mode_init</STRONG> (Thumb, 352 bytes, Stack size 16 bytes, gd32f4xx_dma.o(i.dma_multi_data_mode_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = dma_multi_data_mode_init
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>

<P><STRONG><a name="[12b]"></a>encode_voltage_hex</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, function.o(i.encode_voltage_hex))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = encode_voltage_hex &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_sampling
</UL>

<P><STRONG><a name="[de]"></a>f_close</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, ff.o(i.f_close))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = f_close &rArr; f_sync &rArr; sync &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_config_from_tf
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_and_create_new_file
</UL>

<P><STRONG><a name="[12e]"></a>f_getfree</STRONG> (Thumb, 276 bytes, Stack size 56 bytes, ff.o(i.f_getfree))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = f_getfree &rArr; chk_mounted &rArr; disk_initialize &rArr; sd_bus_mode_config &rArr; sd_bus_width_config &rArr; sd_scr_get &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_self_check
</UL>

<P><STRONG><a name="[10b]"></a>f_mkdir</STRONG> (Thumb, 384 bytes, Stack size 80 bytes, ff.o(i.f_mkdir))
<BR><BR>[Stack]<UL><LI>Max Depth = 408<LI>Call Chain = f_mkdir &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_directories
</UL>

<P><STRONG><a name="[ce]"></a>f_mount</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, ff.o(i.f_mount))
<BR><BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[da]"></a>f_open</STRONG> (Thumb, 364 bytes, Stack size 80 bytes, ff.o(i.f_open))
<BR><BR>[Stack]<UL><LI>Max Depth = 408<LI>Call Chain = f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_sample_data
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_overlimit_data
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_config_from_tf
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_data_storage
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_and_create_new_file
</UL>

<P><STRONG><a name="[df]"></a>f_read</STRONG> (Thumb, 462 bytes, Stack size 64 bytes, ff.o(i.f_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = f_read &rArr; get_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_config_from_tf
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[12d]"></a>f_sync</STRONG> (Thumb, 184 bytes, Stack size 24 bytes, ff.o(i.f_sync))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = f_sync &rArr; sync &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_sample_data
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_overlimit_data
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_log_data
</UL>

<P><STRONG><a name="[dd]"></a>f_write</STRONG> (Thumb, 526 bytes, Stack size 64 bytes, ff.o(i.f_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_sample_data
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_overlimit_data
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_log_data
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[e1]"></a>flash_self_check</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, function.o(i.flash_self_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = flash_self_check &rArr; flash_test_write_read &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_test_write_read
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[135]"></a>flash_test_write_read</STRONG> (Thumb, 124 bytes, Stack size 8 bytes, function.o(i.flash_test_write_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = flash_test_write_read &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memory_compare
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_self_check
</UL>

<P><STRONG><a name="[6a]"></a>fputc</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, usart.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = fputc &rArr; usart_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0printf)
</UL>
<P><STRONG><a name="[c9]"></a>gd_eval_com_init</STRONG> (Thumb, 138 bytes, Stack size 8 bytes, usart.o(i.gd_eval_com_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = gd_eval_com_init &rArr; usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_transmit_config
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_receive_config
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_enable
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[f8]"></a>generate_hide_filename</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, function.o(i.generate_hide_filename))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = generate_hide_filename &rArr; get_datetime_string &rArr; get_current_time &rArr; rtc_current_time_get
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_datetime_string
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_sample_data
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_and_create_new_file
</UL>

<P><STRONG><a name="[13b]"></a>generate_log_filename</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, function.o(i.generate_log_filename))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = generate_log_filename &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_data_storage
</UL>

<P><STRONG><a name="[f9]"></a>generate_overlimit_filename</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, function.o(i.generate_overlimit_filename))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = generate_overlimit_filename &rArr; get_datetime_string &rArr; get_current_time &rArr; rtc_current_time_get
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_datetime_string
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_overlimit_data
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_and_create_new_file
</UL>

<P><STRONG><a name="[f7]"></a>generate_sample_filename</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, function.o(i.generate_sample_filename))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = generate_sample_filename &rArr; get_datetime_string &rArr; get_current_time &rArr; rtc_current_time_get
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_datetime_string
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_sample_data
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_and_create_new_file
</UL>

<P><STRONG><a name="[13c]"></a>get_current_time</STRONG> (Thumb, 170 bytes, Stack size 24 bytes, function.o(i.get_current_time))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = get_current_time &rArr; rtc_current_time_get
</UL>
<BR>[Calls]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_current_time_get
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_sampling
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_oled_display
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_now_command
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_unix_timestamp
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_datetime_string
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_current_time_string
</UL>

<P><STRONG><a name="[13e]"></a>get_current_time_string</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, function.o(i.get_current_time_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = get_current_time_string &rArr; get_current_time &rArr; rtc_current_time_get
</UL>
<BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_current_time
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_log_data
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_sampling
</UL>

<P><STRONG><a name="[13a]"></a>get_datetime_string</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, function.o(i.get_datetime_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = get_datetime_string &rArr; get_current_time &rArr; rtc_current_time_get
</UL>
<BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_current_time
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_sample_filename
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_overlimit_filename
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_hide_filename
</UL>

<P><STRONG><a name="[108]"></a>get_fat</STRONG> (Thumb, 228 bytes, Stack size 24 bytes, ff.o(i.get_fat))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = get_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>

<P><STRONG><a name="[12f]"></a>get_fattime</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, diskio.o(i.get_fattime))
<BR><BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[13f]"></a>get_unix_timestamp</STRONG> (Thumb, 118 bytes, Stack size 32 bytes, function.o(i.get_unix_timestamp))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = get_unix_timestamp &rArr; get_current_time &rArr; rtc_current_time_get
</UL>
<BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_current_time
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_sampling
</UL>

<P><STRONG><a name="[be]"></a>gpio_af_set</STRONG> (Thumb, 94 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_af_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_config
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[aa]"></a>gpio_bit_reset</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_reset))
<BR><BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_sampling
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stop_sampling
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_Byte
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitAck
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Stop
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Start
</UL>

<P><STRONG><a name="[a8]"></a>gpio_bit_set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_set))
<BR><BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_sampling
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_Byte
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitAck
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Stop
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Start
</UL>

<P><STRONG><a name="[e5]"></a>gpio_bit_toggle</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_toggle))
<BR><BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[ad]"></a>gpio_input_bit_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_input_bit_get))
<BR><BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_key234_press
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_key1_press
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitAck
</UL>

<P><STRONG><a name="[a4]"></a>gpio_mode_set</STRONG> (Thumb, 78 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_mode_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_port_init
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_config
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitAck
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Stop
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Start
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Init
</UL>

<P><STRONG><a name="[a7]"></a>gpio_output_options_set</STRONG> (Thumb, 66 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_output_options_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_output_options_set
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_config
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitAck
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Stop
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Start
</UL>

<P><STRONG><a name="[d6]"></a>hide_command</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, function.o(i.hide_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 504<LI>Call Chain = hide_command &rArr; write_log_data &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_log_data
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_command_match
</UL>

<P><STRONG><a name="[cc]"></a>increment_boot_count</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, function.o(i.increment_boot_count))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = increment_boot_count &rArr; save_config_to_flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_config_to_flash
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[cf]"></a>init_data_storage</STRONG> (Thumb, 66 bytes, Stack size 72 bytes, function.o(i.init_data_storage))
<BR><BR>[Stack]<UL><LI>Max Depth = 568<LI>Call Chain = init_data_storage &rArr; write_log_data &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_log_data
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_log_filename
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_directories
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[cb]"></a>load_config_from_flash</STRONG> (Thumb, 240 bytes, Stack size 32 bytes, function.o(i.load_config_from_flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = load_config_from_flash &rArr; spi_flash_buffer_read &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_read_command
</UL>

<P><STRONG><a name="[ca]"></a>local_rtc_init</STRONG> (Thumb, 94 bytes, Stack size 8 bytes, function.o(i.local_rtc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = local_rtc_init &rArr; rcu_osci_stab_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_register_sync_wait
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_rtc_clock_config
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_stab_wait
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_on
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_flag_get
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pmu_backup_write_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_config_command
</UL>

<P><STRONG><a name="[63]"></a>main</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 928<LI>Call Chain = main &rArr; UsrFunction &rArr; update_sampling &rArr; write_overlimit_data &rArr; check_and_create_new_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[e0]"></a>memory_compare</STRONG> (Thumb, 36 bytes, Stack size 12 bytes, function.o(i.memory_compare))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = memory_compare
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_test_write_read
</UL>

<P><STRONG><a name="[c8]"></a>nvic_config</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, function.o(i.nvic_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = nvic_config &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[149]"></a>nvic_irq_enable</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, gd32f4xx_misc.o(i.nvic_irq_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_config
</UL>

<P><STRONG><a name="[148]"></a>nvic_priority_group_set</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_misc.o(i.nvic_priority_group_set))
<BR><BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_config
</UL>

<P><STRONG><a name="[14a]"></a>parse_time_input</STRONG> (Thumb, 404 bytes, Stack size 56 bytes, function.o(i.parse_time_input))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = parse_time_input &rArr; __0sscanf &rArr; __vfscanf_char &rArr; __vfscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_config_command
</UL>

<P><STRONG><a name="[142]"></a>pmu_backup_write_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_pmu.o(i.pmu_backup_write_enable))
<BR><BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_reset_command
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_config_command
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_rtc_init
</UL>

<P><STRONG><a name="[d4]"></a>process_command</STRONG> (Thumb, 566 bytes, Stack size 40 bytes, function.o(i.process_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 744<LI>Call Chain = process_command &rArr; read_config_from_tf &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_file
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;unhide_command
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_self_check
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stop_sampling
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_sampling
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_ratio_value
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_limit_value
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_status_check
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_reset_command
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_now_command
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_config_command
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_config_from_tf
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memory_compare
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hide_command
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;convert_to_bcd
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_command
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_read_command
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[109]"></a>put_fat</STRONG> (Thumb, 310 bytes, Stack size 32 bytes, ff.o(i.put_fat))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>

<P><STRONG><a name="[165]"></a>rcu_bkp_reset_disable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_bkp_reset_disable))
<BR><BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_reset_command
</UL>

<P><STRONG><a name="[164]"></a>rcu_bkp_reset_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_bkp_reset_enable))
<BR><BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_reset_command
</UL>

<P><STRONG><a name="[192]"></a>rcu_clock_freq_get</STRONG> (Thumb, 264 bytes, Stack size 84 bytes, gd32f4xx_rcu.o(i.rcu_clock_freq_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
</UL>

<P><STRONG><a name="[144]"></a>rcu_flag_get</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_stab_wait
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_status_check
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_rtc_init
</UL>

<P><STRONG><a name="[143]"></a>rcu_osci_on</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_osci_on))
<BR><BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_rtc_init
</UL>

<P><STRONG><a name="[146]"></a>rcu_osci_stab_wait</STRONG> (Thumb, 342 bytes, Stack size 20 bytes, gd32f4xx_rcu.o(i.rcu_osci_stab_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = rcu_osci_stab_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_rtc_init
</UL>

<P><STRONG><a name="[a3]"></a>rcu_periph_clock_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_port_init
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_config
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_rtc_init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Init
</UL>

<P><STRONG><a name="[f2]"></a>rcu_periph_reset_disable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_disable))
<BR><BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_deinit
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_deinit
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
</UL>

<P><STRONG><a name="[f1]"></a>rcu_periph_reset_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_enable))
<BR><BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_deinit
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_deinit
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
</UL>

<P><STRONG><a name="[145]"></a>rcu_rtc_clock_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_rtc_clock_config))
<BR><BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_rtc_init
</UL>

<P><STRONG><a name="[15f]"></a>read_adc_voltage</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, function.o(i.read_adc_voltage))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = read_adc_voltage &rArr; ADC_Read_Voltage &rArr; ADC_Read_Value
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Read_Voltage
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_sampling
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_oled_display
</UL>

<P><STRONG><a name="[14d]"></a>read_config_from_tf</STRONG> (Thumb, 400 bytes, Stack size 296 bytes, function.o(i.read_config_from_tf))
<BR><BR>[Stack]<UL><LI>Max Depth = 704<LI>Call Chain = read_config_from_tf &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_config_to_flash
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[c6]"></a>read_device_id_from_flash</STRONG> (Thumb, 70 bytes, Stack size 72 bytes, function.o(i.read_device_id_from_flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = read_device_id_from_flash &rArr; save_device_id_to_flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_device_id_to_flash
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[150]"></a>rtc_config_command</STRONG> (Thumb, 284 bytes, Stack size 120 bytes, function.o(i.rtc_config_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 440<LI>Call Chain = rtc_config_command &rArr; parse_time_input &rArr; __0sscanf &rArr; __vfscanf_char &rArr; __vfscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pmu_backup_write_enable
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_time_input
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_rtc_init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;convert_to_bcd
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[13d]"></a>rtc_current_time_get</STRONG> (Thumb, 96 bytes, Stack size 12 bytes, gd32f4xx_rtc.o(i.rtc_current_time_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = rtc_current_time_get
</UL>
<BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_current_time
</UL>

<P><STRONG><a name="[155]"></a>rtc_init</STRONG> (Thumb, 216 bytes, Stack size 20 bytes, gd32f4xx_rtc.o(i.rtc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_register_sync_wait
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init_mode_exit
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init_mode_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_config_command
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[162]"></a>rtc_init_mode_enter</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, gd32f4xx_rtc.o(i.rtc_init_mode_enter))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rtc_init_mode_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[163]"></a>rtc_init_mode_exit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_init_mode_exit))
<BR><BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[151]"></a>rtc_now_command</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, function.o(i.rtc_now_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = rtc_now_command &rArr; get_current_time &rArr; rtc_current_time_get
</UL>
<BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_current_time
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[147]"></a>rtc_register_sync_wait</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, gd32f4xx_rtc.o(i.rtc_register_sync_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rtc_register_sync_wait
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_rtc_init
</UL>

<P><STRONG><a name="[153]"></a>rtc_reset_command</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, function.o(i.rtc_reset_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = rtc_reset_command &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_bkp_reset_enable
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_bkp_reset_disable
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pmu_backup_write_enable
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[152]"></a>rtc_status_check</STRONG> (Thumb, 264 bytes, Stack size 8 bytes, function.o(i.rtc_status_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = rtc_status_check &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_flag_get
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[f4]"></a>save_config_to_flash</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, function.o(i.save_config_to_flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = save_config_to_flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_ratio_value
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_limit_value
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_config_from_tf
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;increment_boot_count
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_command
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adjust_sample_cycle
</UL>

<P><STRONG><a name="[161]"></a>save_device_id_to_flash</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, function.o(i.save_device_id_to_flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = save_device_id_to_flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_device_id_from_flash
</UL>

<P><STRONG><a name="[11d]"></a>sd_block_read</STRONG> (Thumb, 500 bytes, Stack size 40 bytes, sdcard.o(i.sd_block_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = sd_block_read &rArr; dma_receive_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_enable
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_disable
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_enable
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_disable
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_read
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_datablocksize_get
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>

<P><STRONG><a name="[120]"></a>sd_block_write</STRONG> (Thumb, 760 bytes, Stack size 56 bytes, sdcard.o(i.sd_block_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = sd_block_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_enable
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_disable
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_enable
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_disable
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_write
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_datablocksize_get
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>

<P><STRONG><a name="[11b]"></a>sd_bus_mode_config</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, sdcard.o(i.sd_bus_mode_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = sd_bus_mode_config &rArr; sd_bus_width_config &rArr; sd_scr_get &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_hardware_clock_disable
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_clock_config
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_bus_mode_set
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[118]"></a>sd_card_information_get</STRONG> (Thumb, 686 bytes, Stack size 12 bytes, sdcard.o(i.sd_card_information_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = sd_card_information_get
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[17a]"></a>sd_card_init</STRONG> (Thumb, 268 bytes, Stack size 16 bytes, sdcard.o(i.sd_card_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = sd_card_init &rArr; r6_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_power_state_get
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r6_error_check
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r2_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
</UL>

<P><STRONG><a name="[119]"></a>sd_card_select_deselect</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, sdcard.o(i.sd_card_select_deselect))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = sd_card_select_deselect &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[11a]"></a>sd_cardstatus_get</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, sdcard.o(i.sd_cardstatus_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = sd_cardstatus_get &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[117]"></a>sd_init</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, sdcard.o(i.sd_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = sd_init &rArr; sd_power_on &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_hardware_clock_disable
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_deinit
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_clock_config
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_bus_mode_set
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_config
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_config
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[b9]"></a>sd_interrupts_process</STRONG> (Thumb, 286 bytes, Stack size 8 bytes, sdcard.o(i.sd_interrupts_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = sd_interrupts_process &rArr; sd_transfer_stop &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_flag_get
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_flag_clear
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_disable
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_stop
</UL>
<BR>[Called By]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_IRQHandler
</UL>

<P><STRONG><a name="[11e]"></a>sd_multiblocks_read</STRONG> (Thumb, 632 bytes, Stack size 48 bytes, sdcard.o(i.sd_multiblocks_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = sd_multiblocks_read &rArr; dma_receive_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_enable
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_disable
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_enable
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_disable
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_read
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_datablocksize_get
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>

<P><STRONG><a name="[121]"></a>sd_multiblocks_write</STRONG> (Thumb, 878 bytes, Stack size 56 bytes, sdcard.o(i.sd_multiblocks_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_enable
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_disable
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_enable
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_disable
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_write
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_datablocksize_get
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>

<P><STRONG><a name="[17d]"></a>sd_power_on</STRONG> (Thumb, 290 bytes, Stack size 24 bytes, sdcard.o(i.sd_power_on))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = sd_power_on &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_power_state_set
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_hardware_clock_disable
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_clock_enable
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_clock_config
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_bus_mode_set
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r7_error_check
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r3_error_check
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdsent_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
</UL>

<P><STRONG><a name="[11c]"></a>sd_transfer_mode_config</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, sdcard.o(i.sd_transfer_mode_config))
<BR><BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[17f]"></a>sd_transfer_stop</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, sdcard.o(i.sd_transfer_stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = sd_transfer_stop &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_interrupts_process
</UL>

<P><STRONG><a name="[177]"></a>sdio_bus_mode_set</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_bus_mode_set))
<BR><BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_mode_config
</UL>

<P><STRONG><a name="[176]"></a>sdio_clock_config</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, gd32f4xx_sdio.o(i.sdio_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = sdio_clock_config
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_mode_config
</UL>

<P><STRONG><a name="[183]"></a>sdio_clock_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[157]"></a>sdio_command_index_get</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_command_index_get))
<BR><BR>[Called By]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r6_error_check
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>

<P><STRONG><a name="[16b]"></a>sdio_command_response_config</STRONG> (Thumb, 52 bytes, Stack size 12 bytes, gd32f4xx_sdio.o(i.sdio_command_response_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = sdio_command_response_config
</UL>
<BR>[Called By]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_stop
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_select_deselect
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
</UL>

<P><STRONG><a name="[16d]"></a>sdio_csm_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_csm_enable))
<BR><BR>[Called By]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_stop
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_select_deselect
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
</UL>

<P><STRONG><a name="[166]"></a>sdio_data_config</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, gd32f4xx_sdio.o(i.sdio_data_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = sdio_data_config
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
</UL>

<P><STRONG><a name="[16f]"></a>sdio_data_read</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_data_read))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
</UL>

<P><STRONG><a name="[167]"></a>sdio_data_transfer_config</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_data_transfer_config))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
</UL>

<P><STRONG><a name="[173]"></a>sdio_data_write</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_data_write))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
</UL>

<P><STRONG><a name="[17c]"></a>sdio_deinit</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, gd32f4xx_sdio.o(i.sdio_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = sdio_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
</UL>

<P><STRONG><a name="[169]"></a>sdio_dma_disable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_dma_disable))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[171]"></a>sdio_dma_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_dma_enable))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[168]"></a>sdio_dsm_disable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_dsm_disable))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[16e]"></a>sdio_dsm_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_dsm_enable))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
</UL>

<P><STRONG><a name="[102]"></a>sdio_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r7_error_check
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r6_error_check
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r3_error_check
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r2_error_check
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdsent_error_check
</UL>

<P><STRONG><a name="[101]"></a>sdio_flag_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdsent_error_check
</UL>

<P><STRONG><a name="[178]"></a>sdio_hardware_clock_disable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_hardware_clock_disable))
<BR><BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_mode_config
</UL>

<P><STRONG><a name="[181]"></a>sdio_interrupt_disable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_interrupt_disable))
<BR><BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_interrupts_process
</UL>

<P><STRONG><a name="[170]"></a>sdio_interrupt_enable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[180]"></a>sdio_interrupt_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_interrupts_process
</UL>

<P><STRONG><a name="[17e]"></a>sdio_interrupt_flag_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_interrupts_process
</UL>

<P><STRONG><a name="[17b]"></a>sdio_power_state_get</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_power_state_get))
<BR><BR>[Called By]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
</UL>

<P><STRONG><a name="[182]"></a>sdio_power_state_set</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_power_state_set))
<BR><BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[158]"></a>sdio_response_get</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_response_get))
<BR><BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r6_error_check
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>

<P><STRONG><a name="[16c]"></a>sdio_wait_type_set</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_wait_type_set))
<BR><BR>[Called By]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_stop
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_select_deselect
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
</UL>

<P><STRONG><a name="[14f]"></a>set_limit_value</STRONG> (Thumb, 352 bytes, Stack size 48 bytes, function.o(i.set_limit_value))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = set_limit_value &rArr; __hardfp_atof &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_config_to_flash
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[14e]"></a>set_ratio_value</STRONG> (Thumb, 352 bytes, Stack size 48 bytes, function.o(i.set_ratio_value))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = set_ratio_value &rArr; __hardfp_atof &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_config_to_flash
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[187]"></a>spi_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_enable))
<BR><BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
</UL>

<P><STRONG><a name="[138]"></a>spi_flash_buffer_read</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, spi_flash.o(i.spi_flash_buffer_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = spi_flash_buffer_read &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_device_id_from_flash
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_config_from_flash
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_test_write_read
</UL>

<P><STRONG><a name="[137]"></a>spi_flash_buffer_write</STRONG> (Thumb, 290 bytes, Stack size 40 bytes, spi_flash.o(i.spi_flash_buffer_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_device_id_to_flash
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_config_to_flash
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_test_write_read
</UL>

<P><STRONG><a name="[c5]"></a>spi_flash_init</STRONG> (Thumb, 146 bytes, Stack size 32 bytes, spi_flash.o(i.spi_flash_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = spi_flash_init &rArr; gpio_af_set
</UL>
<BR>[Calls]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[185]"></a>spi_flash_page_write</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, spi_flash.o(i.spi_flash_page_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
</UL>

<P><STRONG><a name="[134]"></a>spi_flash_read_id</STRONG> (Thumb, 78 bytes, Stack size 24 bytes, spi_flash.o(i.spi_flash_read_id))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = spi_flash_read_id &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_self_check
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_self_check
</UL>

<P><STRONG><a name="[136]"></a>spi_flash_sector_erase</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, spi_flash.o(i.spi_flash_sector_erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = spi_flash_sector_erase &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_device_id_to_flash
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_config_to_flash
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_test_write_read
</UL>

<P><STRONG><a name="[184]"></a>spi_flash_send_byte</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, spi_flash.o(i.spi_flash_send_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_transmit
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
</UL>

<P><STRONG><a name="[189]"></a>spi_flash_wait_for_write_end</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, spi_flash.o(i.spi_flash_wait_for_write_end))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = spi_flash_wait_for_write_end &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
</UL>

<P><STRONG><a name="[188]"></a>spi_flash_write_enable</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, spi_flash.o(i.spi_flash_write_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
</UL>

<P><STRONG><a name="[18c]"></a>spi_i2s_data_receive</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>

<P><STRONG><a name="[18b]"></a>spi_i2s_data_transmit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>

<P><STRONG><a name="[18a]"></a>spi_i2s_flag_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>

<P><STRONG><a name="[186]"></a>spi_init</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_init))
<BR><BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
</UL>

<P><STRONG><a name="[d9]"></a>start_sampling</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, function.o(i.start_sampling))
<BR><BR>[Stack]<UL><LI>Max Depth = 504<LI>Call Chain = start_sampling &rArr; write_log_data &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_log_data
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_oled_display
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;toggle_sampling_by_key
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_command_match
</UL>

<P><STRONG><a name="[d8]"></a>stop_sampling</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, function.o(i.stop_sampling))
<BR><BR>[Stack]<UL><LI>Max Depth = 504<LI>Call Chain = stop_sampling &rArr; write_log_data &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_log_data
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_oled_display
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;toggle_sampling_by_key
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_command_match
</UL>

<P><STRONG><a name="[d5]"></a>system_self_check</STRONG> (Thumb, 144 bytes, Stack size 32 bytes, function.o(i.system_self_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = system_self_check &rArr; f_getfree &rArr; chk_mounted &rArr; disk_initialize &rArr; sd_bus_mode_config &rArr; sd_bus_width_config &rArr; sd_scr_get &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[bd]"></a>systick_config</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, systick.o(i.systick_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = systick_config &rArr; NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[fd]"></a>toggle_sampling_by_key</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, function.o(i.toggle_sampling_by_key))
<BR><BR>[Stack]<UL><LI>Max Depth = 512<LI>Call Chain = toggle_sampling_by_key &rArr; stop_sampling &rArr; write_log_data &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stop_sampling
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_sampling
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_key1_press
</UL>

<P><STRONG><a name="[d7]"></a>unhide_command</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, function.o(i.unhide_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 504<LI>Call Chain = unhide_command &rArr; write_log_data &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_log_data
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_command_match
</UL>

<P><STRONG><a name="[f5]"></a>update_oled_display</STRONG> (Thumb, 132 bytes, Stack size 56 bytes, function.o(i.update_oled_display))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = update_oled_display &rArr; OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_adc_voltage
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_current_time
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Refresh
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_sampling
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stop_sampling
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_sampling
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adjust_sample_cycle
</UL>

<P><STRONG><a name="[e4]"></a>update_sampling</STRONG> (Thumb, 478 bytes, Stack size 144 bytes, function.o(i.update_sampling))
<BR><BR>[Stack]<UL><LI>Max Depth = 888<LI>Call Chain = update_sampling &rArr; write_overlimit_data &rArr; check_and_create_new_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_sample_data
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_overlimit_data
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_oled_display
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_adc_voltage
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_unix_timestamp
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_current_time_string
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_current_time
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encode_voltage_hex
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[c0]"></a>usart_baudrate_set</STRONG> (Thumb, 224 bytes, Stack size 32 bytes, gd32f4xx_usart.o(i.usart_baudrate_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[d2]"></a>usart_data_receive</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_file
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_ratio_value
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_limit_value
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_config_command
</UL>

<P><STRONG><a name="[139]"></a>usart_data_transmit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[bf]"></a>usart_deinit</STRONG> (Thumb, 210 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[c3]"></a>usart_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_enable))
<BR><BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[d1]"></a>usart_flag_get</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_file
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_ratio_value
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_limit_value
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_config_command
</UL>

<P><STRONG><a name="[c1]"></a>usart_receive_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_receive_config))
<BR><BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[c2]"></a>usart_transmit_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_transmit_config))
<BR><BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[db]"></a>write_file</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, function.o(i.write_file))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = write_file &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[106]"></a>write_log_data</STRONG> (Thumb, 94 bytes, Stack size 184 bytes, function.o(i.write_log_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 496<LI>Call Chain = write_log_data &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_current_time_string
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;unhide_command
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stop_sampling
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_sampling
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_data_storage
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hide_command
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_command
</UL>

<P><STRONG><a name="[191]"></a>write_overlimit_data</STRONG> (Thumb, 194 bytes, Stack size 256 bytes, function.o(i.write_overlimit_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 744<LI>Call Chain = write_overlimit_data &rArr; check_and_create_new_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_overlimit_filename
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_and_create_new_file
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_sampling
</UL>

<P><STRONG><a name="[190]"></a>write_sample_data</STRONG> (Thumb, 266 bytes, Stack size 88 bytes, function.o(i.write_sample_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 576<LI>Call Chain = write_sample_data &rArr; check_and_create_new_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_sample_filename
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_hide_filename
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_and_create_new_file
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_sampling
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[18f]"></a>NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, systick.o(i.NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_config
</UL>

<P><STRONG><a name="[100]"></a>cmdsent_error_check</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, sdcard.o(i.cmdsent_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = cmdsent_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[122]"></a>dma_receive_config</STRONG> (Thumb, 170 bytes, Stack size 64 bytes, sdcard.o(i.dma_receive_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = dma_receive_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_multi_data_mode_init
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flow_controller_config
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_clear
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_deinit
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_subperipheral_select
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[12a]"></a>dma_transfer_config</STRONG> (Thumb, 172 bytes, Stack size 64 bytes, sdcard.o(i.dma_transfer_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_multi_data_mode_init
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flow_controller_config
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_clear
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_deinit
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_subperipheral_select
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
</UL>

<P><STRONG><a name="[140]"></a>gpio_config</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, sdcard.o(i.gpio_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = gpio_config &rArr; gpio_af_set
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
</UL>

<P><STRONG><a name="[156]"></a>r1_error_check</STRONG> (Thumb, 120 bytes, Stack size 24 bytes, sdcard.o(i.r1_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_index_get
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_type_check
</UL>
<BR>[Called By]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_stop
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_select_deselect
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
</UL>

<P><STRONG><a name="[159]"></a>r1_error_type_check</STRONG> (Thumb, 174 bytes, Stack size 0 bytes, sdcard.o(i.r1_error_type_check))
<BR><BR>[Called By]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>

<P><STRONG><a name="[15a]"></a>r2_error_check</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, sdcard.o(i.r2_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = r2_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
</UL>

<P><STRONG><a name="[15b]"></a>r3_error_check</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, sdcard.o(i.r3_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = r3_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[15c]"></a>r6_error_check</STRONG> (Thumb, 158 bytes, Stack size 24 bytes, sdcard.o(i.r6_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = r6_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_index_get
</UL>
<BR>[Called By]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
</UL>

<P><STRONG><a name="[15d]"></a>r7_error_check</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, sdcard.o(i.r7_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = r7_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[15e]"></a>rcu_config</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, sdcard.o(i.rcu_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rcu_config
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
</UL>

<P><STRONG><a name="[175]"></a>sd_bus_width_config</STRONG> (Thumb, 242 bytes, Stack size 16 bytes, sdcard.o(i.sd_bus_width_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = sd_bus_width_config &rArr; sd_scr_get &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_mode_config
</UL>

<P><STRONG><a name="[174]"></a>sd_card_state_get</STRONG> (Thumb, 166 bytes, Stack size 24 bytes, sdcard.o(i.sd_card_state_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = sd_card_state_get &rArr; sdio_command_response_config
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_index_get
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_type_check
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
</UL>

<P><STRONG><a name="[16a]"></a>sd_datablocksize_get</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, sdcard.o(i.sd_datablocksize_get))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[179]"></a>sd_scr_get</STRONG> (Thumb, 344 bytes, Stack size 32 bytes, sdcard.o(i.sd_scr_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = sd_scr_get &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_read
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
</UL>

<P><STRONG><a name="[18e]"></a>system_clock_240m_25m_hxtal</STRONG> (Thumb, 250 bytes, Stack size 0 bytes, system_gd32f4xx.o(i.system_clock_240m_25m_hxtal))
<BR><BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>

<P><STRONG><a name="[bb]"></a>system_clock_config</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.system_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_240m_25m_hxtal
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[fb]"></a>check_fs</STRONG> (Thumb, 138 bytes, Stack size 16 bytes, ff.o(i.check_fs))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = check_fs &rArr; disk_read &rArr; sd_multiblocks_read &rArr; dma_receive_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
</UL>

<P><STRONG><a name="[10e]"></a>chk_chr</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, ff.o(i.chk_chr))
<BR><BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>

<P><STRONG><a name="[fe]"></a>chk_mounted</STRONG> (Thumb, 898 bytes, Stack size 80 bytes, ff.o(i.chk_mounted))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = chk_mounted &rArr; disk_initialize &rArr; sd_bus_mode_config &rArr; sd_bus_width_config &rArr; sd_scr_get &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
</UL>

<P><STRONG><a name="[107]"></a>create_chain</STRONG> (Thumb, 202 bytes, Stack size 32 bytes, ff.o(i.create_chain))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>

<P><STRONG><a name="[10c]"></a>create_name</STRONG> (Thumb, 336 bytes, Stack size 48 bytes, ff.o(i.create_name))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = create_name &rArr; mem_set
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_chr
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
</UL>

<P><STRONG><a name="[10f]"></a>dir_find</STRONG> (Thumb, 92 bytes, Stack size 24 bytes, ff.o(i.dir_find))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cmp
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
</UL>

<P><STRONG><a name="[113]"></a>dir_next</STRONG> (Thumb, 280 bytes, Stack size 24 bytes, ff.o(i.dir_next))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>
<BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[115]"></a>dir_register</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, ff.o(i.dir_register))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = dir_register &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[110]"></a>dir_sdi</STRONG> (Thumb, 156 bytes, Stack size 24 bytes, ff.o(i.dir_sdi))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = dir_sdi &rArr; get_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[130]"></a>follow_path</STRONG> (Thumb, 158 bytes, Stack size 32 bytes, ff.o(i.follow_path))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[112]"></a>mem_cmp</STRONG> (Thumb, 38 bytes, Stack size 20 bytes, ff.o(i.mem_cmp))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = mem_cmp
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[116]"></a>mem_cpy</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, ff.o(i.mem_cpy))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = mem_cpy
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[10d]"></a>mem_set</STRONG> (Thumb, 20 bytes, Stack size 12 bytes, ff.o(i.mem_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = mem_set
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>

<P><STRONG><a name="[111]"></a>move_window</STRONG> (Thumb, 114 bytes, Stack size 24 bytes, ff.o(i.move_window))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[131]"></a>remove_chain</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, ff.o(i.remove_chain))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = remove_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[132]"></a>sync</STRONG> (Thumb, 202 bytes, Stack size 16 bytes, ff.o(i.sync))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = sync &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_ioctl
</UL>
<BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[133]"></a>validate</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, ff.o(i.validate))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = validate
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
</UL>

<P><STRONG><a name="[ec]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[e7]"></a>_printf_core</STRONG> (Thumb, 1744 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf
</UL>

<P><STRONG><a name="[ef]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[ee]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[6b]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0sprintf)
</UL>
<P><STRONG><a name="[68]"></a>_scanf_char_input</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, scanf_char.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> scanf_char.o(.text)
</UL>
<P><STRONG><a name="[7e]"></a>_local_sscanf</STRONG> (Thumb, 54 bytes, Stack size 56 bytes, strtod.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_real
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strtod_int
</UL>

<P><STRONG><a name="[8d]"></a>_fp_value</STRONG> (Thumb, 296 bytes, Stack size 64 bytes, scanf_fp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
