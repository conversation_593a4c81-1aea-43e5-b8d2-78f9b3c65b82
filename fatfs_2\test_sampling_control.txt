采样控制功能测试用例

测试步骤：
1. 启动采样：start
2. 观察LED1闪烁（1秒周期）
3. 观察OLED显示：
   - 第一行：时间（hh:mm:ss）
   - 第二行：电压值（xx.xx V）
4. 观察串口输出：
   - 每5秒输出一条采样数据
   - 格式：2025-01-01 00:30:05 ch0=10.50V
5. 停止采样：stop
6. 验证LED1熄灭
7. 验证OLED显示"system idle"

预期结果：
- start命令输出：
  Periodic Sampling
  sample cycle: 5s
  2025-01-01 00:30:05 ch0=10.50V
  2025-01-01 00:30:10 ch0=10.50V

- stop命令输出：
  Periodic Sampling STOP

硬件要求：
- LED1连接到PA4引脚
- OLED通过I2C连接（PB8-SCL, PB9-SDA）
- ADC通道连接到PC0引脚
- RTC时钟系统正常工作

测试场景：
1. 正常启动和停止采样
2. 长时间运行稳定性测试
3. 多次启动停止切换测试
4. 电压值精度验证
5. 时间显示准确性验证
