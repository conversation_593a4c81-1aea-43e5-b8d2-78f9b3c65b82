# 按键周期调整功能说明

## 功能概述

本功能实现了通过按键动态调整采样周期的能力，支持5秒、10秒、15秒三种周期模式，并具备配置持久化存储功能。

## 功能特性

### 1. 按键控制
- **KEY1**: 采样启停控制（原有功能）
- **KEY2**: 设置采样周期为5秒
- **KEY3**: 设置采样周期为10秒
- **KEY4**: 设置采样周期为15秒

### 2. 输出格式
按下周期调整按键时，系统输出：
```
=== >sample cycle adjust: 10s
```

### 3. 持久化存储
- 周期设置自动保存到SPI Flash
- 断电重启后配置保持有效
- 与变比(ratio)和阈值(limit)一起存储

### 4. 实时生效
- 采样过程中按键调整周期立即生效
- OLED显示同步更新
- 下次采样按新周期执行

## 技术实现

### 配置数据结构扩展
```c
typedef struct {
    float ratio;        // 变比
    float limit;        // 阈值
    uint32_t cycle;     // 采样周期（秒）
} config_data_t;
```

### 按键检测机制
- 采用边沿触发检测（下降沿）
- 内置防抖处理，避免误触发
- 独立状态标志，防止重复触发

### 周期调整函数
```c
void adjust_sample_cycle(uint32_t new_cycle)
{
    if(new_cycle == 5 || new_cycle == 10 || new_cycle == 15) {
        sample_cycle = new_cycle;
        config_data.cycle = new_cycle;
        
        // 输出周期调整信息
        printf("=== >sample cycle adjust: %lus\r\n", sample_cycle);
        
        // 保存配置到Flash
        save_config_to_flash();
        
        // 如果正在采样，重新启动以应用新周期
        if(sampling_active) {
            last_sample_second = 255; // 重置采样时间点
        }
        
        // 更新OLED显示
        update_oled_display();
    }
}
```

## 使用示例

### 基本操作流程
1. 系统启动，默认采样周期为5秒
2. 按下KEY3，设置周期为10秒
3. 系统输出：`=== >sample cycle adjust: 10s`
4. 启动采样：按下KEY1或输入`start`
5. 观察采样输出，每10秒输出一次数据
6. 采样过程中按下KEY4，调整为15秒周期
7. 系统输出：`=== >sample cycle adjust: 15s`
8. 后续采样按15秒间隔执行

### 预期输出示例
```
=== >sample cycle adjust: 10s
start
Periodic Sampling
sample cycle: 10s
2025-01-01 00:30:00 ch0=10.5V
2025-01-01 00:30:10 ch0=10.6V
2025-01-01 00:30:20 ch0=10.7V
=== >sample cycle adjust: 15s
2025-01-01 00:30:30 ch0=10.8V
2025-01-01 00:30:45 ch0=10.9V
2025-01-01 00:31:00 ch0=11.0V
```

## OLED显示同步

### 显示内容
- **采样模式**: 显示当前时间和电压值
- **空闲模式**: 显示"system idle"
- **周期调整**: 按新周期更新显示频率

### 显示更新机制
- 采样时每个周期更新一次OLED
- 周期调整后立即更新显示
- 确保OLED与串口输出同步

## 配置持久化

### 存储机制
- 使用SPI Flash存储配置数据
- 存储地址：0x001000
- 数据结构：config_data_t (12字节)

### 加载机制
- 系统启动时自动加载配置
- 验证数据有效性
- 无效数据时使用默认值

### 验证规则
```c
if(temp_data.ratio >= 0.0 && temp_data.ratio <= 100.0 &&
   temp_data.limit >= 0.0 && temp_data.limit <= 500.0 &&
   (temp_data.cycle == 5 || temp_data.cycle == 10 || temp_data.cycle == 15)) {
    // 数据有效
} else {
    // 使用默认值
}
```

## 测试验证

### 功能测试
1. **按键响应测试**
   - 分别按下KEY2/3/4，验证周期调整
   - 检查输出格式是否正确

2. **持久化测试**
   - 调整周期后断电重启
   - 验证周期设置是否保持

3. **实时调整测试**
   - 采样过程中调整周期
   - 验证是否立即生效

4. **OLED同步测试**
   - 验证OLED显示与采样周期同步
   - 检查周期调整后显示更新

### 边界条件测试
- 快速连续按键测试
- 采样过程中频繁调整周期
- 断电重启后配置恢复

## 注意事项

1. **按键防抖**: 内置20ms防抖处理，避免误触发
2. **周期限制**: 仅支持5s/10s/15s三种周期
3. **存储保护**: 配置数据自动验证，防止异常值
4. **实时生效**: 周期调整在采样过程中立即生效
5. **OLED同步**: 确保显示更新与采样周期一致

## 故障排除

### 常见问题
1. **按键无响应**: 检查按键硬件连接和初始化
2. **周期不生效**: 验证Flash存储和加载逻辑
3. **OLED不同步**: 检查显示更新机制
4. **配置丢失**: 验证Flash存储完整性

### 调试方法
- 使用串口输出调试信息
- 检查按键状态变量
- 验证配置数据结构
- 监控Flash读写操作
