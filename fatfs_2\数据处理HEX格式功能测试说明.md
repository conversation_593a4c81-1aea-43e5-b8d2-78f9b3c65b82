# 数据处理HEX格式功能测试说明

## 功能概述

通过`hide`和`unhide`指令，可以在正常时间戳格式和HEX格式之间切换采样数据的显示方式。

## 编码规则

### 时间戳编码（4字节）
- **格式**：Unix时间戳转换为HEX
- **示例**：2025-01-01 12:30:45 → 1735705845 → 6774C4F5

### 电压值编码（4字节）
- **整数部分**：2字节（高位在前）
- **小数部分**：2字节（高位在前，小数×65536）
- **示例**：12.5V → 整数12(000C) + 小数0.5×65536=32768(8000) → 000C8000

### 完整示例
```
原始数据：2025-01-01 12:30:45 ch0=12.5V
HEX编码：6774C4F5000C8000
```

## 输出格式

### 正常模式（默认）
```
2025-01-01 00:30:05 ch0=10.5V
2025-01-01 00:30:10 ch0=8.2V
```

### HEX模式（hide后）
```
=== >6774C4F5000C8000
=== >6774C4FA000C8000
```

### 超限情况
#### 正常模式超限
```
=== >2025-01-01 00:30:05 ch0=10.5V OverLimit (10.00)!
```

#### HEX模式超限
```
=== >6774C4F5000C8000*
```

## 命令使用

### 前置条件
**重要**：hide和unhide命令只能在采样运行状态下使用！

### 正确的操作流程
```bash
# 1. 首先启动采样
start
# 输出：
# Periodic Sampling
# sample cycle: 5s

# 2. 切换到HEX模式（仅在采样运行时可用）
hide
# 输出：
# <=== hide
# Data format switched to HEX mode

# 3. 恢复正常模式（仅在采样运行时可用）
unhide
# 输出：
# <=== unhide
# Data format restored to normal mode
```

### 错误情况
```bash
# 在未启动采样时使用hide/unhide
hide
# 输出：
# Error: Sampling not active. Please start sampling first.
```

## 测试步骤

### 1. 基本功能测试
```bash
# 步骤1：启动采样（正常模式）
start
# 观察正常格式输出：2025-01-01 12:00:05 ch0=10.5V

# 步骤2：切换到HEX模式（必须在采样运行时）
hide
# 观察HEX格式输出：=== >6774C4F5000C8000

# 步骤3：恢复正常模式
unhide
# 观察恢复正常格式：2025-01-01 12:00:15 ch0=10.5V

# 步骤4：停止采样（自动恢复正常模式）
stop
# 输出：Periodic Sampling STOP
```

### 2. 超限测试
```bash
# 步骤1：设置较低阈值
limit
# 输入: 5.0

# 步骤2：启动采样
start
# 观察正常格式输出

# 步骤3：切换到HEX模式
hide
# 观察超限时是否有*标记：=== >6774C4F5000C8000*

# 步骤4：验证超限LED2是否点亮
```

### 3. 错误处理测试
```bash
# 测试在未启动采样时使用hide命令
hide
# 预期输出：Error: Sampling not active. Please start sampling first.

# 测试在未启动采样时使用unhide命令
unhide
# 预期输出：Error: Sampling not active. Please start sampling first.
```

### 3. 数据验证
#### 时间戳验证
- 记录HEX时间戳（前8位）
- 转换为十进制Unix时间戳
- 验证与实际时间是否匹配

#### 电压值验证
- 记录HEX电压值（后8位）
- 前4位转换为整数部分
- 后4位除以65536得到小数部分
- 验证与实际电压是否匹配

## 技术实现

### 关键函数

#### Unix时间戳转换
```c
uint32_t get_unix_timestamp(void)
{
    // 基于2000年1月1日基准时间计算
    // 返回当前时间的Unix时间戳
}
```

#### 电压值HEX编码
```c
void encode_voltage_hex(float voltage, char* hex_str)
{
    uint16_t integer_part = (uint16_t)voltage;
    uint16_t decimal_hex = (uint16_t)((voltage - integer_part) * 65536);
    sprintf(hex_str, "%04X%04X", integer_part, decimal_hex);
}
```

### 输出逻辑
```c
if(hide_mode) {
    // HEX格式输出
    uint32_t timestamp = get_unix_timestamp();
    char voltage_hex[9];
    encode_voltage_hex(voltage, voltage_hex);
    
    if(is_overlimit) {
        printf("=== >%08X%s*\r\n", timestamp, voltage_hex);
    } else {
        printf("=== >%08X%s\r\n", timestamp, voltage_hex);
    }
} else {
    // 正常格式输出
    // ...
}
```

## 验证要点

### 1. 格式正确性
- HEX输出为16位字符（8位时间戳+8位电压）
- 超限时正确添加*标记
- 切换命令正确响应

### 2. 数据准确性
- 时间戳转换准确
- 电压值编码准确
- 小数部分计算正确

### 3. 模式切换
- hide/unhide命令正确切换
- 模式状态持续有效
- 重启后恢复默认模式

## 故障排除

### HEX格式错误
1. 检查sprintf格式字符串
2. 验证数据类型转换
3. 确认字节序（高位在前）

### 时间戳不准确
1. 检查RTC时间设置
2. 验证Unix时间戳计算
3. 确认基准时间正确

### 电压编码错误
1. 检查整数/小数分离
2. 验证65536乘法计算
3. 确认数据类型范围

## 示例数据

### 测试用例1
- **时间**：2025-01-01 12:30:45
- **电压**：12.5V
- **Unix时间戳**：1735705845 (0x6774C4F5)
- **电压编码**：12(0x000C) + 0.5×65536=32768(0x8000)
- **完整HEX**：6774C4F5000C8000

### 测试用例2（超限）
- **时间**：2025-01-01 12:30:50
- **电压**：15.3V（假设阈值10.0V）
- **Unix时间戳**：1735705850 (0x6774C4FA)
- **电压编码**：15(0x000F) + 0.3×65536=19660(0x4CCC)
- **完整HEX**：6774C4FA000F4CCC*

## 注意事项

1. **精度限制**：小数部分精度受16位整数限制
2. **时间基准**：基于2000年1月1日计算，需确保RTC设置正确
3. **模式持久性**：hide/unhide状态不保存到Flash，重启后恢复默认
4. **超限标记**：仅在HEX模式下使用*标记，正常模式使用文字说明
