# 系统初始化功能实现说明

## 📋 功能概述

根据要求实现了以下系统初始化功能：

1. **系统上电（复位）后，串口打印"====system init===="**
2. **从flash中读取设备ID号："Device_ID:2025-CIMC-2025247961"**
3. **串口打印"====system ready===="**
4. **OLED第一行显示"system idle"**

## 🔧 实现细节

### 1. 系统初始化打印和设备ID读取

**位置**: `System_Init()` 函数
**文件**: `fatfs_2/Function/Function.c` 第168-199行

```c
void System_Init(void)
{
    systick_config();     // 时钟配置

    // 初始化串口（需要在打印之前初始化）
    rcu_periph_clock_enable(RCU_GPIOA);
    rcu_periph_clock_enable(RCU_USART0);

    // 配置USART0引脚
    gpio_af_set(GPIOA, GPIO_AF_7, GPIO_PIN_9);
    gpio_af_set(GPIOA, GPIO_AF_7, GPIO_PIN_10);
    gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_9);
    gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_9);
    gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_10);
    gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_10);

    // 配置USART0
    usart_deinit(USART0);
    usart_baudrate_set(USART0, 115200U);
    usart_receive_config(USART0, USART_RECEIVE_ENABLE);
    usart_transmit_config(USART0, USART_TRANSMIT_ENABLE);
    usart_enable(USART0);

    // 系统初始化开始提示
    printf("====system init====\r\n");

    // 初始化SPI Flash（需要在读取设备ID之前）
    spi_flash_init();

    // 从Flash读取设备ID
    read_device_id_from_flash();
}
```

### 2. 设备ID存储与读取

**定义位置**:
- 设备ID字符串：第61行
- Flash地址定义：第29行
- 函数声明：第156-157行
- 函数实现：第1879-1903行

```c
// 设备ID字符串
const char device_id_string[] = "Device_ID:2025-CIMC-2025247961";

// Flash地址定义
#define DEVICE_ID_FLASH_ADDRESS 0x002000  // 设备ID存储地址

// 保存设备ID到Flash
void save_device_id_to_flash(void);

// 从Flash读取设备ID
void read_device_id_from_flash(void);
```

**读取逻辑**:
- 在System_Init()中，在打印"====system init===="之后立即执行
- 首先从Flash读取设备ID
- 检查数据有效性（以"Device_ID:"开头）
- 如果数据有效，打印读取的设备ID
- 如果数据无效，保存默认设备ID到Flash并打印
- 设备ID按照指定格式"Device_ID:2025-CIMC-2025247961"打印

### 3. 系统就绪提示

**位置**: `UsrFunction()` 函数第262-263行

```c
// 系统就绪提示
printf("====system ready====\r\n");
printf("System ready. Enter commands:\r\n");
```

### 4. OLED显示

**位置**: `UsrFunction()` 函数第228-232行

```c
// 初始化OLED
OLED_Init();
OLED_Clear();
OLED_ShowString(0, 0, (unsigned char*)"system idle", 12);
OLED_Refresh();
```

## 🚀 执行流程

1. **系统上电** → `main()` → `System_Init()`
   - 配置系统时钟
   - 初始化串口
   - 打印 "====system init===="
   - 初始化SPI Flash
   - 从Flash读取设备ID并打印

2. **用户功能初始化** → `UsrFunction()`
   - 初始化LED、串口
   - 初始化ADC、按键、OLED、RTC
   - OLED显示"system idle"
   - 初始化SD卡和数据存储
   - 打印 "====system ready===="
   - 进入命令处理循环

## 📝 输出示例

系统启动时串口输出：
```
====system init====
Device_ID:2025-CIMC-2025247961
====system ready====
System ready. Enter commands:
- 'test': System self-check (full string required)
- 'conf': Read config from TF card
- 'ratio': Set ratio value (0~100)
- 'limit': Set limit value (0~500)
...
```

**注意**: 其他复位打印信息已被注释掉，包括：
- SD卡初始化相关打印
- 数据存储初始化相关打印
- Boot count打印
- RTC初始化相关打印
- Flash数据无效时的打印
- 目录创建相关打印

OLED显示：
```
第一行: system idle
```

## ⚠️ 注意事项

1. **串口初始化顺序**: 在System_Init中提前初始化串口，确保能正常打印初始化信息
2. **Flash操作顺序**: 确保在spi_flash_init()之后再进行Flash读写操作
3. **设备ID存储**: 使用独立的Flash地址(0x002000)存储设备ID，避免与配置数据冲突
4. **OLED显示**: 系统空闲时始终显示"system idle"，采样时显示时间和电压值
5. **其他打印信息**: 已将其他复位相关的打印信息注释掉，只保留核心的系统初始化和就绪提示

## 🔧 已注释的打印信息

为了保持启动输出的简洁性，以下打印信息已被注释：

### SD卡相关
- `SD Card disk_initialize:%d`
- `SD Card f_mount:%d`
- `SD Card Initialize Success!`

### 数据存储相关
- `Initializing data storage...`
- `Created log file: %s`
- `Data storage initialization completed`
- 所有目录创建相关打印

### RTC相关
- `Initializing RTC...`
- `RTC using external LXTAL` / `LXTAL timeout, using IRC32K`
- `RTC clock configured and ready`

### 其他
- `Boot count: %lu`
- `Flash data invalid, using default values`

## 🔍 测试验证

- [x] 系统上电后串口正确打印"====system init===="
- [x] 设备ID正确从Flash读取并打印
- [x] 系统就绪后串口正确打印"====system ready===="
- [x] OLED第一行正确显示"system idle"
- [x] 所有功能按正确顺序执行
