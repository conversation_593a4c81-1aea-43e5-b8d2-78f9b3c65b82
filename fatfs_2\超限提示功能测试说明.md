# 超限提示功能测试说明

## 功能概述

当采样值超过设定的limit阈值时，系统会：
1. 点亮LED2作为超限指示
2. 在串口输出中添加"OverLimit"字样和具体阈值

## 输出格式

### 正常采样输出
```
2025-01-01 00:30:05 ch0=10.5V
2025-01-01 00:30:10 ch0=8.2V
```

### 超限采样输出
```
=== >2025-01-01 00:30:05 ch0=10.5V OverLimit (10.00)!
=== >2025-01-01 00:30:10 ch0=12.3V OverLimit (10.00)!
```

## 测试步骤

### 1. 设置阈值
```bash
# 设置较低的阈值便于测试（例如5.0V）
limit
# 输入: 5.0
```

### 2. 启动采样
```bash
start
# 观察串口输出和LED2状态
```

### 3. 测试场景

#### 场景A：正常电压（低于阈值）
- **预期结果**：
  - LED2关闭
  - 串口输出正常格式：`2025-01-01 12:00:05 ch0=3.2V`

#### 场景B：超限电压（高于阈值）
- **预期结果**：
  - LED2点亮
  - 串口输出超限格式：`=== >2025-01-01 12:00:10 ch0=6.8V OverLimit (5.00)!`

#### 场景C：电压在阈值附近波动
- **预期结果**：
  - LED2根据实时电压值动态开关
  - 串口输出格式随电压值变化

### 4. 停止采样测试
```bash
stop
# 验证LED2是否关闭
```

## 硬件连接

### LED2
- **引脚**：GPIOA_PIN_5
- **控制**：高电平点亮，低电平熄灭

### 电压输入
- **通道**：ADC ch0
- **范围**：0-500V（通过变比调整）

## 配置参数

### 阈值设置
- **命令**：`limit`
- **范围**：0.0 ~ 500.0
- **存储**：自动保存到Flash

### 变比设置
- **命令**：`ratio`
- **范围**：0.0 ~ 100.0
- **作用**：调整ADC读数到实际电压的转换比例

## 验证要点

1. **LED2控制正确性**
   - 超限时立即点亮
   - 正常时立即熄灭
   - 停止采样时确保熄灭

2. **串口输出格式正确性**
   - 正常：`时间 ch0=电压V`
   - 超限：`=== >时间 ch0=电压V OverLimit (阈值)!`

3. **实时响应性**
   - 每次采样都进行超限检测
   - LED2状态实时更新

4. **配置持久性**
   - 阈值设置保存到Flash
   - 重启后配置保持有效

## 故障排除

### LED2不亮
1. 检查硬件连接
2. 确认电压确实超过阈值
3. 验证LED2_ON()宏定义正确

### 输出格式错误
1. 检查printf格式字符串
2. 确认config_data.limit值正确
3. 验证超限判断逻辑

### 阈值设置无效
1. 检查Flash读写功能
2. 确认配置数据结构正确
3. 验证save_config_to_flash()函数

## 技术实现

### 关键代码位置
- **超限检测**：`update_sampling()` 函数第843-852行
- **LED2控制**：`LED2_ON()` / `LED2_OFF()` 宏
- **停止时清除**：`stop_sampling()` 函数第805行

### 判断逻辑
```c
if(voltage > config_data.limit) {
    LED2_ON();  // 点亮LED2
    printf("=== >%s ch0=%.1fV OverLimit (%.2f)!\r\n", time_str, voltage, config_data.limit);
} else {
    LED2_OFF(); // 关闭LED2
    printf("%s ch0=%.1fV\r\n", time_str, voltage);
}
```

## 注意事项

1. **采样周期**：超限检测与采样同步，默认5秒间隔
2. **精度**：电压显示精度为小数点后1位，阈值显示精度为小数点后2位
3. **存储**：阈值修改后自动保存到Flash，无需手动保存
4. **复位**：系统重启后LED2状态会复位，需重新启动采样
