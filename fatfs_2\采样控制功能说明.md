# 采样控制功能实现说明

## 功能概述
已成功实现采样控制功能，支持通过串口指令start/stop控制采样过程，包括LED1指示灯闪烁、OLED实时显示和定时采样输出。

## 功能特性

### 1. 采样启动 (start命令)
**命令：** `start`

**功能：**
- 启动周期采样模式（默认5秒周期）
- LED1指示灯闪烁（1秒周期）
- OLED实时显示时间与电压值
- 每5秒输出一条采样数据

**输出格式：**
```
start
Periodic Sampling
sample cycle: 5s
2025-01-01 00:30:05 ch0=10.50V
2025-01-01 00:30:10 ch0=10.51V
2025-01-01 00:30:15 ch0=10.52V
...
```

**OLED显示：**
- 第一行：时间（hh:mm:ss格式）
- 第二行：电压值（xx.xx V格式）

### 2. 采样停止 (stop命令)
**命令：** `stop`

**功能：**
- 停止周期采样模式
- LED1常灭
- OLED显示空闲状态

**输出格式：**
```
stop
Periodic Sampling STOP
```

**OLED显示：**
- 第一行：system idle
- 第二行：空

## 技术实现

### 核心变量
```c
uint8_t sampling_active = 0;     // 采样状态：0=停止，1=运行
uint32_t sample_cycle = 5;       // 采样周期（秒）
uint32_t sample_counter = 0;     // 采样计数器
uint32_t led_blink_counter = 0;  // LED闪烁计数器
uint8_t led_state = 0;           // LED状态
```

### 关键函数

#### start_sampling()
- 设置采样状态为活跃
- 重置计数器
- 输出启动信息
- 立即更新OLED显示

#### stop_sampling()
- 设置采样状态为停止
- 关闭LED1
- 输出停止信息
- 更新OLED为空闲状态

#### update_sampling()
- LED1闪烁控制（1秒周期）
- 采样计数器管理
- 定时输出采样数据
- 实时更新OLED显示

#### read_adc_voltage()
- 读取ADC电压值
- 当前为模拟数据（10.00V-10.99V变化）
- 保留小数点后两位

### 时间格式
- **采样输出：** `2025-01-01 00:30:05` (完整日期时间)
- **OLED显示：** `00:30:05` (仅时分秒)
- **电压格式：** `10.50V` (小数点后两位)

## 当前状态

### ✅ 已实现功能
1. **串口命令控制** - start/stop命令正常工作
2. **LED闪烁指示** - 1秒周期闪烁
3. **定时采样输出** - 5秒周期，格式正确
4. **时间戳功能** - 使用真实RTC时间
5. **电压读取** - ADC接口已准备
6. **OLED显示逻辑** - 显示逻辑已实现

### 🔧 待完善功能
1. **OLED硬件驱动** - 需要解决头文件包含问题
2. **真实ADC读取** - 当前使用模拟数据
3. **采样周期配置** - 当前固定5秒

### 📋 临时解决方案
- OLED显示内容通过串口输出：`[OLED] 00:30:05 | 10.50 V`
- 采样时显示：`[OLED] system idle`

## 测试验证

### 基本功能测试
```bash
# 启动采样
start
# 预期输出：
# Periodic Sampling
# sample cycle: 5s
# [OLED] 12:00:05 | 10.50 V
# 2025-01-01 12:00:05 ch0=10.50V
# 2025-01-01 12:00:10 ch0=10.51V

# 停止采样
stop
# 预期输出：
# Periodic Sampling STOP
# [OLED] system idle
```

### LED指示验证
- 启动采样后LED1应该1秒周期闪烁
- 停止采样后LED1应该常灭

### 时间戳验证
- 确保RTC时间正确设置
- 采样输出的时间戳应该准确递增

## 集成说明

### 与现有功能的兼容性
- **RTC时间功能** - 完全集成，采样使用真实时间
- **配置管理** - 独立运行，不影响其他配置
- **串口命令** - 与现有命令体系完全兼容
- **系统自检** - 不影响自检功能

### 性能优化
- 采样更新频率优化，避免过度占用CPU
- LED闪烁使用计数器控制，精确1秒周期
- OLED更新仅在必要时进行

## 后续扩展建议

1. **可配置采样周期**
   ```bash
   # 设置采样周期为10秒
   set cycle 10
   ```

2. **多通道采样**
   ```bash
   # 输出多通道数据
   2025-01-01 12:00:05 ch0=10.50V ch1=5.25V ch2=3.30V
   ```

3. **数据记录功能**
   ```bash
   # 自动保存采样数据到TF卡
   start record
   ```

4. **阈值报警**
   ```bash
   # 电压超过阈值时报警
   2025-01-01 12:00:05 ch0=15.50V [ALARM]
   ```

---
**实现完成时间：** 2025-06-16
**编译状态：** ✅ 通过
**功能状态：** 🟢 核心功能就绪，OLED显示待完善
