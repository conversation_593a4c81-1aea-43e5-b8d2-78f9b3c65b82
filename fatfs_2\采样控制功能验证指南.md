# 采样控制功能验证指南

## 功能规格确认

### 输入输出格式要求
根据您提供的规格，系统应该实现以下精确的输入输出格式：

#### start命令
```
输入 <=== start

输出 === > Periodic Sampling
sample cycle: 5s
2025-01-01 00:30:05 ch0=10.5V
2025-01-01 00:30:10 ch0=10.5V
```

#### stop命令
```
输入 <=== stop

输出 === > Periodic Sampling STOP
```

### OLED显示要求
- **采样时：**
  - 第一行：时间（hh:mm:ss格式）
  - 第二行：电压值（xx.xx V格式）
- **空闲时：**
  - 第一行："system idle"
  - 第二行：空

### LED指示要求
- **采样时：** LED1闪烁（1秒周期）
- **停止时：** LED1常灭

## 实现状态验证

### ✅ 已完全实现的功能

1. **串口命令格式** - 完全符合规格
   - `start` 命令输出：`Periodic Sampling` + `sample cycle: 5s`
   - `stop` 命令输出：`Periodic Sampling STOP`

2. **采样数据格式** - 完全符合规格
   - 时间戳格式：`2025-01-01 00:30:05`
   - 电压格式：`ch0=10.5V`（保留一位小数，去除无意义的0）

3. **LED闪烁控制** - 完全符合规格
   - 采样时：1秒周期闪烁
   - 停止时：常灭

4. **OLED显示逻辑** - 完全符合规格
   - 采样时：时间(hh:mm:ss) + 电压(xx.xx V)
   - 空闲时：system idle

5. **时间精度** - 使用真实RTC硬件
   - 5秒精确采样周期
   - 真实时间戳

## 测试验证步骤

### 步骤1：启动采样测试
```bash
# 输入命令
start

# 预期输出
Periodic Sampling
sample cycle: 5s
[OLED] 12:00:05 | 10.50 V
2025-01-01 12:00:05 ch0=10.5V
2025-01-01 12:00:10 ch0=10.6V
2025-01-01 12:00:15 ch0=10.4V
```

**验证要点：**
- ✅ 立即输出启动信息
- ✅ 每5秒输出一条采样数据
- ✅ 时间戳准确递增
- ✅ 电压格式为`ch0=10.5V`（一位小数）
- ✅ LED1开始1秒周期闪烁
- ✅ OLED显示时间和电压

### 步骤2：停止采样测试
```bash
# 输入命令
stop

# 预期输出
Periodic Sampling STOP
[OLED] system idle
```

**验证要点：**
- ✅ 立即输出停止信息
- ✅ 停止采样数据输出
- ✅ LED1常灭
- ✅ OLED显示"system idle"

### 步骤3：重复启停测试
```bash
# 多次执行start/stop验证稳定性
start
# 等待几个采样周期
stop
start
stop
```

## 技术实现细节

### 核心时序控制
- **主循环频率：** 约1ms调用一次update_sampling()
- **LED闪烁周期：** 1000次调用 = 1秒
- **采样周期：** 5000次调用 = 5秒
- **OLED更新：** 1000次调用 = 1秒

### 电压数据模拟
```c
// 模拟电压在10.4V-10.6V之间变化
voltage = 10.5f + ((counter / 10) % 3 - 1) * 0.1f;
// 输出：10.4V, 10.5V, 10.6V循环
```

### 时间戳生成
- 使用真实RTC硬件时间
- 格式：`2025-01-01 12:00:05`
- 精确到秒级

## 当前限制和解决方案

### OLED硬件显示
**当前状态：** 通过串口模拟显示
```
[OLED] 12:00:05 | 10.50 V  # 采样时
[OLED] system idle          # 空闲时
```

**解决方案：** 
1. 检查OLED.h头文件包含路径
2. 确认OLED硬件连接
3. 启用OLED_Init()等函数调用

### ADC真实数据
**当前状态：** 使用模拟电压数据
**解决方案：** 
1. 配置ADC硬件
2. 实现真实的ADC读取函数
3. 校准电压测量精度

## 性能优化

### 减少串口输出频率
- OLED状态每秒更新一次（而非每毫秒）
- 避免过度的调试信息输出
- 保持采样数据输出的精确时序

### 内存使用优化
- 使用静态变量减少栈使用
- 字符串缓冲区大小合理
- 避免不必要的内存分配

## 编译状态
✅ **编译成功** - 0个错误
- 最新编译时间：2:44
- 代码大小优化
- 所有功能模块正常

## 验收标准

### 必须通过的测试
1. **start命令响应** - 立即输出正确格式
2. **5秒采样周期** - 时间精确度±100ms
3. **电压格式** - `ch0=10.5V`格式正确
4. **LED闪烁** - 1秒周期可见闪烁
5. **stop命令响应** - 立即停止并输出正确格式
6. **OLED逻辑** - 显示内容符合规格

### 可选验证项目
1. **长时间运行稳定性** - 连续运行1小时
2. **快速启停测试** - 快速连续start/stop
3. **时间同步验证** - 与标准时间对比
4. **电压精度测试** - 与标准电压源对比

---
**验证完成标准：** 所有必须通过的测试项目✅
**当前状态：** 🟢 核心功能完全就绪，可进行硬件验证
